/*! For license information please see beta-features-tracking-modal.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,t,o)=>{var r=o(51609),a=Symbol.for("react.element"),n=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),c=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function i(e,t,o){var r,i={},d=null,w=null;for(r in void 0!==o&&(d=""+o),void 0!==t.key&&(d=""+t.key),void 0!==t.ref&&(w=t.ref),t)n.call(t,r)&&!s.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:d,ref:w,props:i,_owner:c.current}}t.jsx=i,t.jsxs=i},39793:(e,t,o)=>{e.exports=o(94931)},51609:e=>{e.exports=window.React}},t={};const o=window.wp.element,r=window.wp.i18n,a=window.wp.components,n=window.wp.data,c=window.wp.compose,s=window.wc.data,i=window.wc.tracks,d=window.wc.explat;var w=function o(r){var a=t[r];if(void 0!==a)return a.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,o),n.exports}(39793);const u=(0,c.compose)((0,n.withDispatch)((e=>{const{updateOptions:t}=e(s.optionsStore);return{updateOptions:t}})))((({updateOptions:e})=>{const[t,n]=(0,o.useState)(!1),[c,s]=(0,o.useState)(!1),u=(0,o.useRef)(document.querySelector("#woocommerce_navigation_enabled")),l=async t=>("function"==typeof window.wcTracks.enable&&(t?window.wcTracks.enable((()=>{(0,d.initializeExPlat)()})):window.wcTracks.isEnabled=!1),t&&(0,i.recordEvent)("settings_features_tracking_enabled"),e({woocommerce_allow_tracking:t?"yes":"no"}));return(0,o.useEffect)((()=>{if(!u.current)return;const e=e=>{e.target.checked&&(e.target.checked=!1,n(!0))},t=u.current;return t.addEventListener("change",e,!1),()=>t.removeEventListener("change",e)}),[]),u.current&&t?(0,w.jsxs)(a.Modal,{title:(0,r.__)("Build a Better WooCommerce","woocommerce"),onRequestClose:()=>n(!1),className:"woocommerce-beta-features-tracking-modal",children:[(0,w.jsxs)("p",{children:[(0,r.__)("Testing new features requires sharing non-sensitive data via ","woocommerce"),(0,w.jsx)("a",{href:"https://woocommerce.com/usage-tracking?utm_medium=product",children:(0,r.__)("usage tracking","woocommerce")}),(0,r.__)(". Gathering usage data allows us to make WooCommerce better — your store will be considered as we evaluate new features, judge the quality of an update, or determine if an improvement makes sense. No personal data is tracked or stored and you can opt-out at any time.","woocommerce")]}),(0,w.jsx)("div",{className:"woocommerce-beta-features-tracking-modal__checkbox",children:(0,w.jsx)(a.CheckboxControl,{label:"Enable usage tracking",onChange:s,checked:c})}),(0,w.jsx)("div",{className:"woocommerce-beta-features-tracking-modal__actions",children:(0,w.jsx)(a.Button,{isPrimary:!0,onClick:async()=>{c?(await l(!0),u.current.checked=!0):await l(!1),n(!1)},children:(0,r.__)("Save","woocommerce")})})]}):null})),l=document.createElement("div");l.setAttribute("id","beta-features-tracking"),(0,o.createRoot)(document.body.appendChild(l)).render((0,w.jsx)(u,{})),(window.wc=window.wc||{}).betaFeaturesTrackingModal={}})();
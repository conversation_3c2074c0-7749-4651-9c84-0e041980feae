"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[4925],{94925:(e,o,t)=>{t.r(o),t.d(o,{default:()=>h});var r=t(86087),s=t(27723),i=t(98846),n=t(47778),c=t(83306),d=t(49362),l=t(64769),p=t(39793);const u=({shouldTourBeShown:e,dismissModal:o})=>{const{isNewUser:t,loadingPublishedProductsCount:u}=(0,l.B)();(0,r.useEffect)((()=>{e&&(0,c.recordEvent)("block_product_editor_spotlight_view")}),[e]);const[a,m]=(0,r.useState)(!1),{maybeShowFeedbackBar:_}=(0,n.__experimentalUseFeedbackBar)();if(u)return null;if(a)return(0,p.jsx)(d.A,{isNewUser:t,onCloseGuide:(e,t)=>{o(),"finish"===t?(0,c.recordEvent)("block_product_editor_spotlight_tell_me_more_click"):(0,c.recordEvent)("block_product_editor_spotlight_dismissed",{current_page:e+1}),m(!1),_()}});if(e){const{heading:e,description:r}={heading:t?(0,s.__)("Meet the product editing form","woocommerce"):(0,s.__)("Welcome to the new product form!","woocommerce"),description:t?(0,s.__)("Discover the product form's unique features with a quick overview of what's included.","woocommerce"):(0,s.__)("Discover its new features and improvements with a quick overview of what's included.","woocommerce")};return(0,p.jsx)(i.TourKit,{config:{steps:[{meta:{name:"woocommerce-block-editor-tour",primaryButton:{text:(0,s.__)("View highlights","woocommerce")},descriptions:{desktop:r},heading:(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("span",{children:e}),(0,p.jsx)(i.Pill,{children:(0,s.__)("Beta","woocommerce")})]})},referenceElements:{desktop:"#adminmenuback"}}],closeHandler:(e,t,r)=>{"done-btn"===r?((0,c.recordEvent)("block_product_editor_spotlight_view_highlights"),m(!0)):(o(),(0,c.recordEvent)("block_product_editor_spotlight_dismissed",{current_page:0}),_())},options:{effects:{arrowIndicator:!1,overlay:!1,liveResize:{rootElementSelector:"#adminmenuback",resize:!0}},portalParentElement:document.getElementById("wpbody"),popperModifiers:[{name:"bottom-left",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:({state:e})=>{e.styles.popper.top="auto",e.styles.popper.left="auto",e.styles.popper.bottom="10px",e.styles.popper.transform="translate3d(10px, 0px, 0px)"}}],classNames:"woocommerce-block-editor-tourkit"}}})}return null};var a=t(40314),m=t(47143);const _="woocommerce_block_product_tour_shown",h=()=>{const e=(()=>{const{updateOptions:e}=(0,m.useDispatch)(a.optionsStore),{shouldTourBeShown:o}=(0,m.useSelect)((e=>{const{getOption:o,hasFinishedResolution:t}=e(a.optionsStore);return{shouldTourBeShown:!("yes"===o(_)||!t("getOption",[_]))}}),[]);return{dismissModal:()=>{e({[_]:"yes"})},shouldTourBeShown:o}})();return(0,p.jsx)(u,{...e})}}}]);
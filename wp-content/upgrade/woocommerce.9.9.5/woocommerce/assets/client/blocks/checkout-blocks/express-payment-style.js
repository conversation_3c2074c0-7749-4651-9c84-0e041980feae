"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[3574],{21321:(t,s,o)=>{o.r(s),o.d(s,{default:()=>a});var e=o(89525),n=o(4898),u=o(59030),l=o(55370),r=o(10790);const a=t=>{const s=(0,e.N)(l.attributes,t),{showButtonStyles:o,buttonHeight:a,buttonBorderRadius:c}=s;return(0,r.jsx)(u.W.Provider,{value:{showButtonStyles:o,buttonHeight:a,buttonBorderRadius:c},children:(0,r.jsx)(n.A,{})})}}}]);
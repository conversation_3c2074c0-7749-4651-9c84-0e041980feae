(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[3982],{4223:(t,e,c)=>{"use strict";c.r(e),c.d(e,{default:()=>m});var o=c(1616),r=c(4921),a=c(5703),n=c(2024),l=c(7355),s=c(4908),u=c(8696),i=c(4656),p=c(1e3),d=c(7723);const h=(0,d.__)("Place Order","woocommerce"),b=(0,d.__)("Return to Cart","woocommerce");c(7883);var k=c(790);const w={placeOrderButtonLabel:{type:"string",default:h},returnToCartButtonLabel:{type:"string",default:b}};var C=c(602);const m=(0,o.withFilteredAttributes)({...w,...C.attributes})((({cartPageId:t,showReturnToCart:e,className:c,placeOrderButtonLabel:o,returnToCartButtonLabel:d,priceSeparator:b})=>{const{paymentMethodButtonLabel:w}=(0,s.w)(),C=(0,p.applyCheckoutFilter)({filterName:"placeOrderButtonLabel",defaultValue:w||o||h}),m=c?.includes("is-style-with-price")||!1;return(0,k.jsxs)("div",{className:(0,r.A)("wc-block-checkout__actions",c),children:[(0,k.jsx)(i.StoreNoticesContainer,{context:u.tG.CHECKOUT_ACTIONS}),(0,k.jsxs)("div",{className:"wc-block-checkout__actions_row",children:[e&&(0,k.jsx)(n.A,{href:(0,a.getSetting)("page-"+t,!1),children:d}),m&&(0,k.jsx)("style",{children:`.wp-block-woocommerce-checkout-actions-block {\n\t\t\t\t\t\t.wc-block-components-checkout-place-order-button__separator {\n\t\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\t\tcontent: "${b}";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}`}),(0,k.jsx)(l.A,{label:C,fullWidth:!e,showPrice:m,priceSeparator:b})]})]})}))},7883:()=>{}}]);
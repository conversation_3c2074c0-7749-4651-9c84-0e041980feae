"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[5806],{28819:(s,e,t)=>{t.r(e),t.d(e,{default:()=>b});var i=t(4921),c=t(41616),o=t(14656),l=t(87792),n=t(47143),p=t(47594),h=t(94199),r=t(87631),a=t(62852),u=t(10790);const b=(0,c.withFilteredAttributes)(a.A)((({title:s,description:e,children:t,className:c})=>{const{showFormStepNumbers:a}=(0,h.O)(),b=(0,n.useSelect)((s=>s(p.checkoutStore).isProcessing())),{showShippingMethods:d}=(0,l.C)();return d?(0,u.jsxs)(o.FormStep,{id:"shipping-option",disabled:b,className:(0,i.A)("wc-block-checkout__shipping-option",c),title:s,description:e,showStepNumber:a,children:[(0,u.jsx)(r.A,{}),t]}):null}))}}]);
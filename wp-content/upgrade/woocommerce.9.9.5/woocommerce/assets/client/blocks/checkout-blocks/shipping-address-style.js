"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[636],{80513:(s,e,i)=>{i.r(e),i.d(e,{default:()=>u});var t=i(4921),c=i(41616),l=i(14656),o=i(87792),n=i(47143),p=i(47594),r=i(65780),h=i(81509),a=i(94199),d=i(10790);const u=(0,c.withFilteredAttributes)(h.A)((({title:s,description:e,children:i,className:c})=>{const h=(0,n.useSelect)((s=>s(p.checkoutStore).isProcessing())),{showShippingFields:u}=(0,o.C)(),{showFormStepNumbers:b}=(0,a.O)();return u?(0,d.jsxs)(l.FormStep,{id:"shipping-fields",disabled:h,className:(0,t.A)("wc-block-checkout__shipping-fields",c),title:s,description:e,showStepNumber:b,children:[(0,d.jsx)(r.A,{}),i]}):null}))}}]);
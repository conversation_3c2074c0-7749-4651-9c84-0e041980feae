"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[9691],{4532:(e,s,c)=>{c.r(s),c.d(s,{default:()=>n});var a=c(4656),r=c(910),t=c(5460),o=c(790);const n=({className:e=""})=>{const{cartFees:s,cartTotals:c}=(0,t.V)(),n=(0,r.getCurrencyFromPriceResponse)(c);return(0,o.jsx)(a.Totals<PERSON>rapper,{className:e,children:(0,o.jsx)(a.TotalsFees,{currency:n,cartFees:s})})}}}]);
"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[3398],{46917:(e,t,c)=>{c.r(t),c.d(t,{default:()=>b});var o=c(4921),s=c(41616),n=c(14656),i=c(47143),l=c(47594),r=c(94199),a=c(24850),d=c(62434),h=c(27723),u=c(15703),p=c(78331),k=c(10790);const m=`${p.aW}?redirect_to=${encodeURIComponent(window.location.href)}`,w=()=>{const e=(0,i.useSelect)((e=>e(l.checkoutStore).getCustomerId()));return!(0,u.getSetting)("checkoutShowLoginReminder",!0)||e?null:(0,k.jsx)("a",{className:"wc-block-checkout__login-prompt",href:m,children:(0,h.__)("Log in","woocommerce")})},b=(0,s.withFilteredAttributes)(d.A)((({title:e,description:t,children:c,className:s})=>{const d=(0,i.useSelect)((e=>e(l.checkoutStore).isProcessing())),{showFormStepNumbers:h}=(0,r.O)();return(0,k.jsxs)(n.FormStep,{id:"contact-fields",disabled:d,className:(0,o.A)("wc-block-checkout__contact-fields",s),title:e,description:t,showStepNumber:h,stepHeadingContent:()=>(0,k.jsx)(w,{}),children:[(0,k.jsx)(a.A,{}),c]})}))}}]);
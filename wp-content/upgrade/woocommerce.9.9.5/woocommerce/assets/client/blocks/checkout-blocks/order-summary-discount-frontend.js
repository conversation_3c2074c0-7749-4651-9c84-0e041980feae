"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[2996],{5889:(e,o,s)=>{s.r(o),s.d(o,{default:()=>i});var c=s(5486),n=s(4656),t=s(910),r=s(5460),a=s(5954),u=s(1e3),p=s(790);const l=()=>{const{extensions:e,receiveCart:o,...s}=(0,r.V)(),c={extensions:e,cart:s,context:"woocommerce/checkout"};return(0,p.jsx)(u.ExperimentalDiscountsMeta.Slot,{...c})},i=({className:e=""})=>{const{cartTotals:o,cartCoupons:s}=(0,r.V)(),{removeCoupon:u,isRemovingCoupon:i}=(0,a.k)("wc/checkout"),C=(0,t.getCurrencyFromPriceResponse)(o);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(n.TotalsWrapper,{className:e,children:(0,p.jsx)(c.n$,{cartCoupons:s,currency:C,isRemovingCoupon:i,removeCoupon:u,values:o})}),(0,p.jsx)(l,{})]})}}}]);
"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[3644],{5405:(s,c,e)=>{e.r(c),e.d(c,{default:()=>n});var t=e(6788),a=e(5460),r=e(4656),o=e(790);const n=({className:s="",disableProductDescriptions:c=!1})=>{const{cartItems:e}=(0,a.V)();return(0,o.jsx)(r.TotalsWrapper,{className:s,children:(0,o.jsx)(t.A,{cartItems:e,disableProductDescriptions:c})})}}}]);
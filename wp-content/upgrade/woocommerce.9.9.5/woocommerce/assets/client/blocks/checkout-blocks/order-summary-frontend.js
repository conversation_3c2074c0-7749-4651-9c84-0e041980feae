"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[12],{4439:(e,c,s)=>{s.r(c),s.d(c,{default:()=>w});var r=s(5486),o=s(910),t=s(5460),n=s(7723),a=s(4530),l=s(559),i=s(2174),m=s(6087),d=s(4921),h=s(8889),u=s(4575),k=s(2919),p=s(4656),x=s(790);const j=({children:e,stepHeadingContent:c})=>(0,x.jsxs)("div",{className:"wc-block-components-checkout-step__heading",children:[(0,x.jsx)(p.Title,{"aria-hidden":"true",className:"wc-block-components-checkout-step__title",headingLevel:"2",children:e}),!!c&&(0,x.jsx)("span",{className:"wc-block-components-checkout-step__heading-content",children:c})]}),w=({children:e,className:c=""})=>{const{cartTotals:s}=(0,t.V)(),{isLarge:p}=(0,u.G)(),[w,b]=(0,m.useState)(!1),y=(0,o.getCurrencyFromPriceResponse)(s),_=parseInt(s.total_price,10),v=(0,m.useId)(),N=p?{}:{role:"button",onClick:()=>b(!w),"aria-expanded":w,"aria-controls":v,tabIndex:0,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||b(!w)}};return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)("div",{className:c,children:[(0,x.jsxs)("div",{className:(0,d.A)("wc-block-components-checkout-order-summary__title",{"is-open":w}),...N,children:[(0,x.jsx)("p",{className:"wc-block-components-checkout-order-summary__title-text",role:"heading",children:(0,n.__)("Order summary","woocommerce")}),!p&&(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(k.FormattedMonetaryAmount,{currency:y,value:_}),(0,x.jsx)(a.A,{className:"wc-block-components-checkout-order-summary__title-icon",icon:w?l.A:i.A})]})]}),(0,x.jsxs)("div",{className:(0,d.A)("wc-block-components-checkout-order-summary__content",{"is-open":w}),id:v,children:[e,(0,x.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,x.jsx)(r.Ay,{currency:y,values:s})}),(0,x.jsx)(h.Xm,{})]})]}),!p&&(0,x.jsx)(h.iG,{children:(0,x.jsxs)("div",{className:`${c} checkout-order-summary-block-fill-wrapper`,children:[(0,x.jsx)(j,{children:(0,x.jsx)(x.Fragment,{children:(0,n.__)("Order summary","woocommerce")})}),(0,x.jsxs)("div",{className:"checkout-order-summary-block-fill",children:[e,(0,x.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,x.jsx)(r.Ay,{currency:y,values:s})}),(0,x.jsx)(h.Xm,{})]})]})})]})}},8889:(e,c,s)=>{s.d(c,{VM:()=>l,Xm:()=>n,iG:()=>a});var r=s(1e3),o=s(5460),t=s(790);const n=()=>{const{extensions:e,receiveCart:c,...s}=(0,o.V)(),n={extensions:e,cart:s,context:"woocommerce/checkout"};return(0,t.jsx)(r.ExperimentalOrderMeta.Slot,{...n})},{Fill:a,Slot:l}=(0,r.createSlotFill)("checkoutOrderSummaryActionArea")}}]);
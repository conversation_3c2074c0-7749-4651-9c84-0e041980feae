(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[636],{9184:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var d=t(7723),r=t(3993),i=t(5703),n=t(2785),o=t(1069),a=t(8537),c=(t(8796),t(790));const l=({address:e,onEdit:s,target:t,isExpanded:l})=>{const p=(0,i.getSetting)("countryData",{});let h=(0,i.getSetting)("defaultAddressFormat","{name}\n{company}\n{address_1}\n{address_2}\n{city}\n{state}\n{postcode}\n{country}");(0,r.objectHasProp)(p,e?.country)&&(0,r.objectHasProp)(p[e.country],"format")&&(0,r.isString)(p[e.country].format)&&(h=p[e.country].format);const{name:g,address:u}=(0,n.M0)(e,h),m="shipping"===t?(0,d.__)("Edit shipping address","woocommerce"):(0,d.__)("Edit billing address","woocommerce");return(0,c.jsxs)("div",{className:"wc-block-components-address-card",children:[(0,c.jsxs)("address",{children:[(0,c.jsx)("span",{className:"wc-block-components-address-card__address-section",children:(0,a.decodeEntities)(g)}),(0,c.jsx)("div",{className:"wc-block-components-address-card__address-section",children:u.filter((e=>!!e)).map(((e,s)=>(0,c.jsx)("span",{children:(0,a.decodeEntities)(e)},"address-"+s)))}),e.phone?(0,c.jsx)("div",{className:"wc-block-components-address-card__address-section",children:e.phone},"address-phone"):""]}),s&&(0,c.jsx)(o.$,{render:(0,c.jsx)("span",{}),className:"wc-block-components-address-card__edit","aria-controls":t,"aria-expanded":l,"aria-label":m,onClick:e=>{e.preventDefault(),s()},type:"button",children:(0,d.__)("Edit","woocommerce")})]})}},7403:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var d=t(4921),r=(t(1121),t(790));const i=({isEditing:e=!1,addressCard:s,addressForm:t})=>{const i=(0,d.A)("wc-block-components-address-address-wrapper",{"is-editing":e});return(0,r.jsxs)("div",{className:i,children:[(0,r.jsx)("div",{className:"wc-block-components-address-card-wrapper",children:s}),(0,r.jsx)("div",{className:"wc-block-components-address-form-wrapper",children:t})]})}},5299:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var d=t(7723);const r=({defaultTitle:e=(0,d.__)("Step","woocommerce"),defaultDescription:s=(0,d.__)("Step description text.","woocommerce"),defaultShowStepNumber:t=!0})=>({title:{type:"string",default:e},description:{type:"string",default:s},showStepNumber:{type:"boolean",default:t}})},8554:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var d=t(4921),r=t(1616),i=t(4656),n=t(7792),o=t(7143),a=t(7594),c=t(7723),l=t(6087),p=t(5929),h=t(7370),g=t(8696),u=t(3001),m=t(5703),b=t(4982),S=t(5336),w=t(7052),_=t(8331),k=t(7403),f=t(9184),j=t(790);const x=()=>{const{shippingAddress:e,setShippingAddress:s,setBillingAddress:t,useShippingAsBilling:d,editingShippingAddress:r,setEditingShippingAddress:i}=(0,n.C)(),{dispatchCheckoutEvent:c}=(0,w.y)(),{hasValidationErrors:p,invalidProps:h}=(0,o.useSelect)((s=>{const t=s(a.validationStore);return{hasValidationErrors:t.hasValidationErrors(),invalidProps:Object.keys(e).filter((e=>void 0!==t.getValidationError("shipping_"+e))).filter(Boolean)}}),[e]);(0,l.useEffect)((()=>{h.length>0&&!1===r&&i(!0)}),[r,p,h.length,i]);const g=(0,l.useCallback)((e=>{s(e),d&&(t(e),c("set-billing-address")),c("set-shipping-address")}),[c,t,s,d]);return(0,j.jsx)(k.A,{isEditing:r,addressCard:(0,j.jsx)(f.A,{address:e,target:"shipping",onEdit:()=>{i(!0)},isExpanded:r}),addressForm:(0,j.jsx)(S.l,{id:"shipping",addressType:"shipping",onChange:g,values:e,fields:_.Hw,isEditing:r})})},A=()=>{const{defaultFields:e,setBillingAddress:s,shippingAddress:t,billingAddress:d,useShippingAsBilling:r,setUseShippingAsBilling:S,setEditingBillingAddress:w}=(0,n.C)(),{isEditor:_}=(0,h.m)(),k=0===(0,m.getSetting)("currentUserId"),f=()=>{const r={...t};e?.phone?.hidden&&delete r.phone,e?.company?.hidden&&delete r.company,(Object.keys(r).length!==Object.keys(d).length||!Object.keys(r).every((e=>r[e]===d[e])))&&s(r)};(0,p.Su)((()=>{r&&f()}));const A=_?u.A:l.Fragment,y=r?[g.tG.SHIPPING_ADDRESS,g.tG.BILLING_ADDRESS]:[g.tG.SHIPPING_ADDRESS],{cartDataLoaded:E}=(0,o.useSelect)((e=>({cartDataLoaded:e(a.cartStore).hasFinishedResolution("getCartData")})));return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(i.StoreNoticesContainer,{context:y}),(0,j.jsx)(A,{children:E?(0,j.jsx)(x,{}):null}),(0,j.jsx)(i.CheckboxControl,{className:"wc-block-checkout__use-address-for-billing",label:(0,c.__)("Use same address for billing","woocommerce"),checked:r,onChange:e=>{S(e),e?f():(w(!0),(e=>{if(!e||!k)return;const t=(0,b.ln)(e);s(t)})(d))}})]})},y={...(0,t(5299).A)({defaultTitle:(0,c.__)("Shipping address","woocommerce"),defaultDescription:(0,c.__)("Enter the address where you want your order delivered.","woocommerce")}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};var E=t(4199);const v=(0,r.withFilteredAttributes)(y)((({title:e,description:s,children:t,className:r})=>{const c=(0,o.useSelect)((e=>e(a.checkoutStore).isProcessing())),{showShippingFields:l}=(0,n.C)(),{showFormStepNumbers:p}=(0,E.O)();return l?(0,j.jsxs)(i.FormStep,{id:"shipping-fields",disabled:c,className:(0,d.A)("wc-block-checkout__shipping-fields",r),title:e,description:s,showStepNumber:p,children:[(0,j.jsx)(A,{}),t]}):null}))},8796:()=>{},1121:()=>{}}]);
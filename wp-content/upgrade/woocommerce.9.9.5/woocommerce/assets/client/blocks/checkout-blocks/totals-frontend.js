"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[8268],{9737:(c,e,s)=>{s.r(e),s.d(e,{default:()=>k});var o=s(4921),t=s(389),a=s(4656),r=s(843),n=s(4575),h=s(790);const k=({children:c,className:e})=>{const[s,k,l]=(0,r.E)(),i=k.height<l.height,{isLarge:u}=(0,n.G)();return(0,h.jsxs)(t.A,{ref:s,className:(0,o.A)("wc-block-checkout__sidebar",e,{"is-sticky":i,"is-large":u}),children:[(0,h.jsx)(a<PERSON><PERSON><PERSON>,{context:"woocommerce/checkout-totals-block"}),c]})}}}]);
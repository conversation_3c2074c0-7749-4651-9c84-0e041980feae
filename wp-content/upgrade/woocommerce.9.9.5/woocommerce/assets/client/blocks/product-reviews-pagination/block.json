{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-reviews-pagination", "title": "Reviews Pagination", "category": "woocommerce", "ancestor": ["woocommerce/blockified-product-reviews"], "allowedBlocks": ["woocommerce/product-reviews-pagination-previous", "woocommerce/product-reviews-pagination-numbers", "woocommerce/product-reviews-pagination-next"], "description": "Displays a paginated navigation to next/previous set of product reviews, when applicable.", "textdomain": "woocommerce", "attributes": {"paginationArrow": {"type": "string", "default": "none"}}, "example": {"attributes": {"paginationArrow": "none"}}, "providesContext": {"reviews/paginationArrow": "paginationArrow"}, "supports": {"align": true, "reusable": false, "html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "layout": {"allowSwitching": false, "allowInheriting": false, "default": {"type": "flex"}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}}}
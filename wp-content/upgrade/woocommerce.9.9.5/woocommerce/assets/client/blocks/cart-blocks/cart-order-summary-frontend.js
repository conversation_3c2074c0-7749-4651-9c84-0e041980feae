"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[9837],{7269:(e,c,s)=>{s.r(c),s.d(c,{default:()=>i});var r=s(5486),t=s(910),n=s(5460),o=s(1e3),a=s(790);const l=()=>{const{extensions:e,receiveCart:c,...s}=(0,n.V)(),r={extensions:e,cart:s,context:"woocommerce/cart"};return(0,a.jsx)(o.ExperimentalOrderMeta.Slot,{...r})},i=({children:e,className:c=""})=>{const{cartTotals:s}=(0,n.V)(),o=(0,t.getCurrencyFromPriceResponse)(s);return(0,a.jsxs)("div",{className:c,children:[e,(0,a.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,a.jsx)(r.Ay,{currency:o,values:s})}),(0,a.jsx)(l,{})]})}}}]);
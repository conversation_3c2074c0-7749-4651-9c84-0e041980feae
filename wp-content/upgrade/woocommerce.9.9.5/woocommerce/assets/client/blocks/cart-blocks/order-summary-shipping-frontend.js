"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[6945],{8847:(e,s,c)=>{c.r(s),c.d(s,{default:()=>w});var o=c(7723),l=c(6087),a=c(5486),t=c(6222),n=c(7370),i=c(5460),r=c(1e3),p=c(3932),h=c(5703),u=c(8331),d=c(8228),m=c(790);const g=()=>{const{shippingRates:e,isLoadingRates:s}=(0,i.V)();return(0,m.jsxs)("fieldset",{className:"wc-block-components-totals-shipping__fieldset",children:[(0,m.jsx)("legend",{className:"screen-reader-text",children:(0,o.__)("Shipping options","woocommerce")}),(0,m.jsx)(d.A,{className:"wc-block-components-totals-shipping__options",shippingRates:e,isLoadingRates:s,context:"woocommerce/cart"})]})},w=({className:e})=>{const{isEditor:s}=(0,n.m)(),{cartNeedsShipping:c,shippingRates:d}=(0,i.V)(),[w,_]=(0,l.useState)(!1);if(!c)return null;if(s&&0===(0,p.T4)(d))return null;const k=(0,h.getSetting)("isShippingCalculatorEnabled",!0)&&u.mH,b=(0,p.PU)(d);return(0,m.jsx)(r.TotalsWrapper,{className:e,children:(0,m.jsx)(t.S.Provider,{value:{showCalculator:k,shippingCalculatorID:"shipping-calculator-form-wrapper",isShippingCalculatorOpen:w,setIsShippingCalculatorOpen:_},children:(0,m.jsx)(a.w7,{label:b?(0,o.__)("Pickup","woocommerce"):(0,o.__)("Delivery","woocommerce"),placeholder:k?null:(0,m.jsx)("span",{className:"wc-block-components-shipping-placeholder__value",children:(0,o.__)("Calculated at checkout","woocommerce")}),collaterals:(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(g,{}),!k&&(0,p.$u)(d)&&(0,m.jsx)("div",{className:"wc-block-components-totals-shipping__delivery-options-notice",children:(0,o.__)("Delivery options will be calculated during checkout","woocommerce")})]})})})})}}}]);
(globalThis.webpackChunkwebpackWcBlocksFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksFrontendJsonp||[]).push([[763],{9044:(e,t,n)=>{"use strict";n.d(t,{A:()=>k});var r=n(2509),o=n(6087),i=n(7697),s=n.n(i),a=n(8468),l=n(4040),c=n.n(l),u=n(9491),p=n(7276),d=n(8596);const f=(0,o.createElement)("div",{className:"event-catcher"}),m=({eventHandlers:e,child:t,childrenWithPopover:n})=>(0,o.cloneElement)((0,o.createElement)("span",{className:"disabled-element-wrapper"},(0,o.cloneElement)(f,e),(0,o.cloneElement)(t,{children:n}),","),e),h=({child:e,eventHandlers:t,childrenWithPopover:n})=>(0,o.cloneElement)(e,{...t,children:n}),g=(e,t,n)=>{if(1!==o.Children.count(e))return;const r=o.Children.only(e);"function"==typeof r.props[t]&&r.props[t](n)},v=function({children:e,position:t,text:n,shortcut:r}){const[i,s]=(0,o.useState)(!1),[l,c]=(0,o.useState)(!1),f=(0,u.useDebounce)(c,700),v=t=>{g(e,"onMouseDown",t),document.addEventListener("mouseup",w),s(!0)},y=t=>{g(e,"onMouseUp",t),document.removeEventListener("mouseup",w),s(!1)},b=e=>"mouseUp"===e?y:"mouseDown"===e?v:void 0,w=b("mouseUp"),E=(t,n)=>r=>{if(g(e,t,r),r.currentTarget.disabled)return;if("focus"===r.type&&i)return;f.cancel();const o=(0,a.includes)(["focus","mouseenter"],r.type);o!==l&&(n?f(o):c(o))},x=()=>{f.cancel(),document.removeEventListener("mouseup",w)};if((0,o.useEffect)((()=>x),[]),1!==o.Children.count(e))return e;const k={onMouseEnter:E("onMouseEnter",!0),onMouseLeave:E("onMouseLeave"),onClick:E("onClick"),onFocus:E("onFocus"),onBlur:E("onBlur"),onMouseDown:b("mouseDown")},T=o.Children.only(e),{children:S,disabled:A}=T.props,C=A?m:h,O=(({grandchildren:e,isOver:t,position:n,text:r,shortcut:i})=>(0,o.concatChildren)(e,t&&(0,o.createElement)(p.A,{focusOnMount:!1,position:n,className:"components-tooltip","aria-hidden":"true",animate:!1,noArrow:!0},r,(0,o.createElement)(d.A,{className:"components-tooltip__shortcut",shortcut:i}))))({grandchildren:S,isOver:l,position:t,text:n,shortcut:r});return C({child:T,eventHandlers:k,childrenWithPopover:O})};var y=n(5573),b=n(1011);const w=function({icon:e=null,size:t=24,...n}){if("string"==typeof e)return(0,o.createElement)(b.A,(0,r.A)({icon:e},n));if((0,o.isValidElement)(e)&&b.A===e.type)return(0,o.cloneElement)(e,{...n});if("function"==typeof e)return e.prototype instanceof o.Component?(0,o.createElement)(e,{size:t,...n}):e({size:t,...n});if(e&&("svg"===e.type||e.type===y.SVG)){const r={width:t,height:t,...e.props,...n};return(0,o.createElement)(y.SVG,r)}return(0,o.isValidElement)(e)?(0,o.cloneElement)(e,{size:t,...n}):e};var E=n(606);const x=["onMouseDown","onClick"],k=(0,o.forwardRef)((function(e,t){const{href:n,target:i,isSmall:l,isPressed:u,isBusy:p,isDestructive:d,className:f,disabled:m,icon:h,iconPosition:g="left",iconSize:y,showTooltip:b,tooltipPosition:k,shortcut:T,label:S,children:A,text:C,variant:O,__experimentalIsFocusable:N,describedBy:I,..._}=function({isDefault:e,isPrimary:t,isSecondary:n,isTertiary:r,isLink:o,variant:i,...s}){let a=i;var l,u,p,d,f;return t&&(null!==(l=a)&&void 0!==l||(a="primary")),r&&(null!==(u=a)&&void 0!==u||(a="tertiary")),n&&(null!==(p=a)&&void 0!==p||(a="secondary")),e&&(c()("Button isDefault prop",{since:"5.4",alternative:'variant="secondary"'}),null!==(d=a)&&void 0!==d||(a="secondary")),o&&(null!==(f=a)&&void 0!==f||(a="link")),{...s,variant:a}}(e),L=s()("components-button",f,{"is-secondary":"secondary"===O,"is-primary":"primary"===O,"is-small":l,"is-tertiary":"tertiary"===O,"is-pressed":u,"is-busy":p,"is-link":"link"===O,"is-destructive":d,"has-text":!!h&&!!A,"has-icon":!!h}),M=m&&!N,D=void 0===n||M?"button":"a",R="a"===D?{href:n,target:i}:{type:"button",disabled:M,"aria-pressed":u};if(m&&N){R["aria-disabled"]=!0;for(const e of x)_[e]=e=>{e.stopPropagation(),e.preventDefault()}}const P=!M&&(b&&S||T||!!S&&(!A||(0,a.isArray)(A)&&!A.length)&&!1!==b),F=I?(0,a.uniqueId)():null,j=_["aria-describedby"]||F,B=(0,o.createElement)(D,(0,r.A)({},R,_,{className:L,"aria-label":_["aria-label"]||S,"aria-describedby":j,ref:t}),h&&"left"===g&&(0,o.createElement)(w,{icon:h,size:y}),C&&(0,o.createElement)(o.Fragment,null,C),h&&"right"===g&&(0,o.createElement)(w,{icon:h,size:y}),A);return P?(0,o.createElement)(o.Fragment,null,(0,o.createElement)(v,{text:I||S,shortcut:T,position:k},B),I&&(0,o.createElement)(E.A,null,(0,o.createElement)("span",{id:F},I))):(0,o.createElement)(o.Fragment,null,B,I&&(0,o.createElement)(E.A,null,(0,o.createElement)("span",{id:F},I)))}))},1011:(e,t,n)=>{"use strict";if(n.d(t,{A:()=>i}),/^(432|454|819|915)$/.test(n.j))var r=n(2509);var o=n(6087);const i=/^(432|454|819|915)$/.test(n.j)?function({icon:e,className:t,...n}){const i=["dashicon","dashicons","dashicons-"+e,t].filter(Boolean).join(" ");return(0,o.createElement)("span",(0,r.A)({className:i},n))}:null},4642:(e,t,n)=>{"use strict";n.d(t,{A:()=>C});var r=n(6087),o=n(8468),i=n(7697),s=n.n(i),a=n(7723),l=n(9491),c=n(8558),u=n(923),p=n.n(u),d=n(5521),f=n(9044),m=n(606);function h({value:e,status:t,title:n,displayTransform:i,isBorderless:c=!1,disabled:u=!1,onClickRemove:p=o.noop,onMouseEnter:g,onMouseLeave:v,messages:y,termPosition:b,termsCount:w}){const E=(0,l.useInstanceId)(h),x=s()("components-form-token-field__token",{"is-error":"error"===t,"is-success":"success"===t,"is-validating":"validating"===t,"is-borderless":c,"is-disabled":u}),k=i(e),T=(0,a.sprintf)(
/* translators: 1: term name, 2: term position in a set of terms, 3: total term set count. */
/* translators: 1: term name, 2: term position in a set of terms, 3: total term set count. */
(0,a.__)("%1$s (%2$s of %3$s)"),k,b,w);return(0,r.createElement)("span",{className:x,onMouseEnter:g,onMouseLeave:v,title:n},(0,r.createElement)("span",{className:"components-form-token-field__token-text",id:`components-form-token-field__token-text-${E}`},(0,r.createElement)(m.A,{as:"span"},T),(0,r.createElement)("span",{"aria-hidden":"true"},k)),(0,r.createElement)(f.A,{className:"components-form-token-field__remove-token",icon:d.A,onClick:!u&&(()=>p({value:e})),label:y.remove,"aria-describedby":`components-form-token-field__token-text-${E}`}))}var g=n(2509);class v extends r.Component{constructor(){super(...arguments),this.onChange=this.onChange.bind(this),this.bindInput=this.bindInput.bind(this)}focus(){this.input.focus()}hasFocus(){return this.input===this.input.ownerDocument.activeElement}bindInput(e){this.input=e}onChange(e){this.props.onChange({value:e.target.value})}render(){const{value:e,isExpanded:t,instanceId:n,selectedSuggestionIndex:o,className:i,...a}=this.props,l=e?e.length+1:0;return(0,r.createElement)("input",(0,g.A)({ref:this.bindInput,id:`components-form-token-input-${n}`,type:"text"},a,{value:e||"",onChange:this.onChange,size:l,className:s()(i,"components-form-token-field__input"),autoComplete:"off",role:"combobox","aria-expanded":t,"aria-autocomplete":"list","aria-owns":t?`components-form-token-suggestions-${n}`:void 0,"aria-activedescendant":-1!==o?`components-form-token-suggestions-${n}-${o}`:void 0,"aria-describedby":`components-form-token-suggestions-howto-${n}`}))}}const y=v;var b=n(6406),w=n.n(b);class E extends r.Component{constructor(){super(...arguments),this.handleMouseDown=this.handleMouseDown.bind(this),this.bindList=this.bindList.bind(this)}componentDidUpdate(){this.props.selectedIndex>-1&&this.props.scrollIntoView&&this.list.children[this.props.selectedIndex]&&(this.scrollingIntoView=!0,w()(this.list.children[this.props.selectedIndex],this.list,{onlyScrollIfNeeded:!0}),this.props.setTimeout((()=>{this.scrollingIntoView=!1}),100))}bindList(e){this.list=e}handleHover(e){return()=>{this.scrollingIntoView||this.props.onHover(e)}}handleClick(e){return()=>{this.props.onSelect(e)}}handleMouseDown(e){e.preventDefault()}computeSuggestionMatch(e){const t=this.props.displayTransform(this.props.match||"").toLocaleLowerCase();if(0===t.length)return null;const n=(e=this.props.displayTransform(e)).toLocaleLowerCase().indexOf(t);return{suggestionBeforeMatch:e.substring(0,n),suggestionMatch:e.substring(n,n+t.length),suggestionAfterMatch:e.substring(n+t.length)}}render(){return(0,r.createElement)("ul",{ref:this.bindList,className:"components-form-token-field__suggestions-list",id:`components-form-token-suggestions-${this.props.instanceId}`,role:"listbox"},(0,o.map)(this.props.suggestions,((e,t)=>{const n=this.computeSuggestionMatch(e),o=s()("components-form-token-field__suggestion",{"is-selected":t===this.props.selectedIndex});return(0,r.createElement)("li",{id:`components-form-token-suggestions-${this.props.instanceId}-${t}`,role:"option",className:o,key:null!=e&&e.value?e.value:this.props.displayTransform(e),onMouseDown:this.handleMouseDown,onClick:this.handleClick(e),onMouseEnter:this.handleHover(e),"aria-selected":t===this.props.selectedIndex},n?(0,r.createElement)("span",{"aria-label":this.props.displayTransform(e)},n.suggestionBeforeMatch,(0,r.createElement)("strong",{className:"components-form-token-field__suggestion-match"},n.suggestionMatch),n.suggestionAfterMatch):this.props.displayTransform(e))})))}}E.defaultProps={match:"",onHover:()=>{},onSelect:()=>{},suggestions:Object.freeze([])};const x=(0,l.withSafeTimeout)(E);var k=n(195);const T=(0,l.createHigherOrderComponent)((e=>t=>(0,r.createElement)(e,(0,g.A)({},t,{speak:k.speak,debouncedSpeak:(0,l.useDebounce)(k.speak,500)}))),"withSpokenMessages"),S={incompleteTokenValue:"",inputOffsetFromEnd:0,isActive:!1,isExpanded:!1,selectedSuggestionIndex:-1,selectedSuggestionScroll:!1};class A extends r.Component{constructor(){super(...arguments),this.state=S,this.onKeyDown=this.onKeyDown.bind(this),this.onKeyPress=this.onKeyPress.bind(this),this.onFocus=this.onFocus.bind(this),this.onBlur=this.onBlur.bind(this),this.deleteTokenBeforeInput=this.deleteTokenBeforeInput.bind(this),this.deleteTokenAfterInput=this.deleteTokenAfterInput.bind(this),this.addCurrentToken=this.addCurrentToken.bind(this),this.onContainerTouched=this.onContainerTouched.bind(this),this.renderToken=this.renderToken.bind(this),this.onTokenClickRemove=this.onTokenClickRemove.bind(this),this.onSuggestionHovered=this.onSuggestionHovered.bind(this),this.onSuggestionSelected=this.onSuggestionSelected.bind(this),this.onInputChange=this.onInputChange.bind(this),this.bindInput=this.bindInput.bind(this),this.bindTokensAndInput=this.bindTokensAndInput.bind(this),this.updateSuggestions=this.updateSuggestions.bind(this)}componentDidUpdate(e){this.state.isActive&&!this.input.hasFocus()&&this.input.focus();const{suggestions:t,value:n}=this.props,r=!p()(t,e.suggestions);(r||n!==e.value)&&this.updateSuggestions(r)}static getDerivedStateFromProps(e,t){return e.disabled&&t.isActive?{isActive:!1,incompleteTokenValue:""}:null}bindInput(e){this.input=e}bindTokensAndInput(e){this.tokensAndInput=e}onFocus(e){const{__experimentalExpandOnFocus:t}=this.props;this.input.hasFocus()||e.target===this.tokensAndInput?this.setState({isActive:!0,isExpanded:!!t||this.state.isExpanded}):this.setState({isActive:!1}),"function"==typeof this.props.onFocus&&this.props.onFocus(e)}onBlur(){this.inputHasValidValue()?this.setState({isActive:!1}):this.setState(S)}onKeyDown(e){let t=!1;switch(e.keyCode){case c.BACKSPACE:t=this.handleDeleteKey(this.deleteTokenBeforeInput);break;case c.ENTER:t=this.addCurrentToken();break;case c.LEFT:t=this.handleLeftArrowKey();break;case c.UP:t=this.handleUpArrowKey();break;case c.RIGHT:t=this.handleRightArrowKey();break;case c.DOWN:t=this.handleDownArrowKey();break;case c.DELETE:t=this.handleDeleteKey(this.deleteTokenAfterInput);break;case c.SPACE:this.props.tokenizeOnSpace&&(t=this.addCurrentToken());break;case c.ESCAPE:t=this.handleEscapeKey(e),e.stopPropagation()}t&&e.preventDefault()}onKeyPress(e){let t=!1;44===e.charCode&&(t=this.handleCommaKey()),t&&e.preventDefault()}onContainerTouched(e){e.target===this.tokensAndInput&&this.state.isActive&&e.preventDefault()}onTokenClickRemove(e){this.deleteToken(e.value),this.input.focus()}onSuggestionHovered(e){const t=this.getMatchingSuggestions().indexOf(e);t>=0&&this.setState({selectedSuggestionIndex:t,selectedSuggestionScroll:!1})}onSuggestionSelected(e){this.addNewToken(e)}onInputChange(e){const t=e.value,n=this.props.tokenizeOnSpace?/[ ,\t]+/:/[,\t]+/,r=t.split(n),i=(0,o.last)(r)||"";r.length>1&&this.addNewTokens(r.slice(0,-1)),this.setState({incompleteTokenValue:i},this.updateSuggestions),this.props.onInputChange(i)}handleDeleteKey(e){let t=!1;return this.input.hasFocus()&&this.isInputEmpty()&&(e(),t=!0),t}handleLeftArrowKey(){let e=!1;return this.isInputEmpty()&&(this.moveInputBeforePreviousToken(),e=!0),e}handleRightArrowKey(){let e=!1;return this.isInputEmpty()&&(this.moveInputAfterNextToken(),e=!0),e}handleUpArrowKey(){return this.setState(((e,t)=>({selectedSuggestionIndex:(0===e.selectedSuggestionIndex?this.getMatchingSuggestions(e.incompleteTokenValue,t.suggestions,t.value,t.maxSuggestions,t.saveTransform).length:e.selectedSuggestionIndex)-1,selectedSuggestionScroll:!0}))),!0}handleDownArrowKey(){return this.setState(((e,t)=>({selectedSuggestionIndex:(e.selectedSuggestionIndex+1)%this.getMatchingSuggestions(e.incompleteTokenValue,t.suggestions,t.value,t.maxSuggestions,t.saveTransform).length,selectedSuggestionScroll:!0}))),!0}handleEscapeKey(e){return this.setState({incompleteTokenValue:e.target.value,isExpanded:!1,selectedSuggestionIndex:-1,selectedSuggestionScroll:!1}),!0}handleCommaKey(){return this.inputHasValidValue()&&this.addNewToken(this.state.incompleteTokenValue),!0}moveInputToIndex(e){this.setState(((t,n)=>({inputOffsetFromEnd:n.value.length-Math.max(e,-1)-1})))}moveInputBeforePreviousToken(){this.setState(((e,t)=>({inputOffsetFromEnd:Math.min(e.inputOffsetFromEnd+1,t.value.length)})))}moveInputAfterNextToken(){this.setState((e=>({inputOffsetFromEnd:Math.max(e.inputOffsetFromEnd-1,0)})))}deleteTokenBeforeInput(){const e=this.getIndexOfInput()-1;e>-1&&this.deleteToken(this.props.value[e])}deleteTokenAfterInput(){const e=this.getIndexOfInput();e<this.props.value.length&&(this.deleteToken(this.props.value[e]),this.moveInputToIndex(e))}addCurrentToken(){let e=!1;const t=this.getSelectedSuggestion();return t?(this.addNewToken(t),e=!0):this.inputHasValidValue()&&(this.addNewToken(this.state.incompleteTokenValue),e=!0),e}addNewTokens(e){const t=(0,o.uniq)(e.map(this.props.saveTransform).filter(Boolean).filter((e=>!this.valueContainsToken(e))));if(t.length>0){const e=(0,o.clone)(this.props.value);e.splice.apply(e,[this.getIndexOfInput(),0].concat(t)),this.props.onChange(e)}}addNewToken(e){const{__experimentalExpandOnFocus:t,__experimentalValidateInput:n}=this.props;n(e)?(this.addNewTokens([e]),this.props.speak(this.props.messages.added,"assertive"),this.setState({incompleteTokenValue:"",selectedSuggestionIndex:-1,selectedSuggestionScroll:!1,isExpanded:!t}),this.state.isActive&&this.input.focus()):this.props.speak(this.props.messages.__experimentalInvalid,"assertive")}deleteToken(e){const t=this.props.value.filter((t=>this.getTokenValue(t)!==this.getTokenValue(e)));this.props.onChange(t),this.props.speak(this.props.messages.removed,"assertive")}getTokenValue(e){return"object"==typeof e?e.value:e}getMatchingSuggestions(e=this.state.incompleteTokenValue,t=this.props.suggestions,n=this.props.value,r=this.props.maxSuggestions,i=this.props.saveTransform){let s=i(e);const a=[],l=[];return 0===s.length?t=(0,o.difference)(t,n):(s=s.toLocaleLowerCase(),(0,o.each)(t,(e=>{const t=e.toLocaleLowerCase().indexOf(s);-1===n.indexOf(e)&&(0===t?a.push(e):t>0&&l.push(e))})),t=a.concat(l)),(0,o.take)(t,r)}getSelectedSuggestion(){if(-1!==this.state.selectedSuggestionIndex)return this.getMatchingSuggestions()[this.state.selectedSuggestionIndex]}valueContainsToken(e){return(0,o.some)(this.props.value,(t=>this.getTokenValue(e)===this.getTokenValue(t)))}getIndexOfInput(){return this.props.value.length-this.state.inputOffsetFromEnd}isInputEmpty(){return 0===this.state.incompleteTokenValue.length}inputHasValidValue(){return this.props.saveTransform(this.state.incompleteTokenValue).length>0}updateSuggestions(e=!0){const{__experimentalExpandOnFocus:t}=this.props,{incompleteTokenValue:n}=this.state,r=n.trim().length>1,o=this.getMatchingSuggestions(n),i=o.length>0,s={isExpanded:t||r&&i};if(e&&(s.selectedSuggestionIndex=-1,s.selectedSuggestionScroll=!1),this.setState(s),r){const{debouncedSpeak:e}=this.props;e(i?(0,a.sprintf)(
/* translators: %d: number of results. */
/* translators: %d: number of results. */
(0,a._n)("%d result found, use up and down arrow keys to navigate.","%d results found, use up and down arrow keys to navigate.",o.length),o.length):(0,a.__)("No results."),"assertive")}}renderTokensAndInput(){const e=(0,o.map)(this.props.value,this.renderToken);return e.splice(this.getIndexOfInput(),0,this.renderInput()),e}renderToken(e,t,n){const o=this.getTokenValue(e),i=e.status?e.status:void 0,s=t+1,a=n.length;return(0,r.createElement)(h,{key:"token-"+o,value:o,status:i,title:e.title,displayTransform:this.props.displayTransform,onClickRemove:this.onTokenClickRemove,isBorderless:e.isBorderless||this.props.isBorderless,onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,disabled:"error"!==i&&this.props.disabled,messages:this.props.messages,termsCount:a,termPosition:s})}renderInput(){const{autoCapitalize:e,autoComplete:t,maxLength:n,placeholder:o,value:i,instanceId:s}=this.props;let a={instanceId:s,autoCapitalize:e,autoComplete:t,placeholder:0===i.length?o:"",ref:this.bindInput,key:"input",disabled:this.props.disabled,value:this.state.incompleteTokenValue,onBlur:this.onBlur,isExpanded:this.state.isExpanded,selectedSuggestionIndex:this.state.selectedSuggestionIndex};return n&&i.length>=n||(a={...a,onChange:this.onInputChange}),(0,r.createElement)(y,a)}render(){const{disabled:e,label:t=(0,a.__)("Add item"),instanceId:n,className:o,__experimentalShowHowTo:i}=this.props,{isExpanded:l}=this.state,c=s()(o,"components-form-token-field__input-container",{"is-active":this.state.isActive,"is-disabled":e});let u={className:"components-form-token-field",tabIndex:"-1"};const p=this.getMatchingSuggestions();return e||(u=Object.assign({},u,{onKeyDown:this.onKeyDown,onKeyPress:this.onKeyPress,onFocus:this.onFocus})),(0,r.createElement)("div",u,(0,r.createElement)("label",{htmlFor:`components-form-token-input-${n}`,className:"components-form-token-field__label"},t),(0,r.createElement)("div",{ref:this.bindTokensAndInput,className:c,tabIndex:"-1",onMouseDown:this.onContainerTouched,onTouchStart:this.onContainerTouched},this.renderTokensAndInput(),l&&(0,r.createElement)(x,{instanceId:n,match:this.props.saveTransform(this.state.incompleteTokenValue),displayTransform:this.props.displayTransform,suggestions:p,selectedIndex:this.state.selectedSuggestionIndex,scrollIntoView:this.state.selectedSuggestionScroll,onHover:this.onSuggestionHovered,onSelect:this.onSuggestionSelected})),i&&(0,r.createElement)("p",{id:`components-form-token-suggestions-howto-${n}`,className:"components-form-token-field__help"},this.props.tokenizeOnSpace?(0,a.__)("Separate with commas, spaces, or the Enter key."):(0,a.__)("Separate with commas or the Enter key.")))}}A.defaultProps={suggestions:Object.freeze([]),maxSuggestions:100,value:Object.freeze([]),displayTransform:o.identity,saveTransform:e=>e.trim(),onChange:()=>{},onInputChange:()=>{},isBorderless:!1,disabled:!1,tokenizeOnSpace:!1,messages:{added:(0,a.__)("Item added."),removed:(0,a.__)("Item removed."),remove:(0,a.__)("Remove item"),__experimentalInvalid:(0,a.__)("Invalid item")},__experimentalExpandOnFocus:!1,__experimentalValidateInput:()=>!0,__experimentalShowHowTo:!0};const C=T((0,l.withInstanceId)(A))},7276:(e,t,n)=>{"use strict";n.d(t,{A:()=>j});var r=n(2509),o=n(6087),i=n(7697),s=n.n(i),a=n(8107),l=n(4040),c=n.n(l),u=n(9491),p=n(5573);const d=(0,o.createElement)(p.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(p.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"}));var f=n(7723);function m(e,t,n){const{defaultView:r}=t,{frameElement:o}=r;if(!o||t===n.ownerDocument)return e;const i=o.getBoundingClientRect();return new r.DOMRect(e.left+i.left,e.top+i.top,e.width,e.height)}var h=n(9044);let g=0;function v(e){const t=document.scrollingElement||document.body;e&&(g=t.scrollTop);const n=e?"add":"remove";t.classList[n]("lockscroll"),document.documentElement.classList[n]("lockscroll"),e||(t.scrollTop=g)}let y=0;function b(){return(0,o.useEffect)((()=>(0===y&&v(!0),++y,()=>{1===y&&v(!1),--y})),[]),null}var w=n(5947),E=n(5291),x=n(8468),k=n(4855);class T extends o.Component{constructor(){super(...arguments),this.isUnmounted=!1,this.bindNode=this.bindNode.bind(this)}componentDidMount(){const{registerSlot:e}=this.props;e(this.props.name,this)}componentWillUnmount(){const{unregisterSlot:e}=this.props;this.isUnmounted=!0,e(this.props.name,this)}componentDidUpdate(e){const{name:t,unregisterSlot:n,registerSlot:r}=this.props;e.name!==t&&(n(e.name),r(t,this))}bindNode(e){this.node=e}forceUpdate(){this.isUnmounted||super.forceUpdate()}render(){const{children:e,name:t,fillProps:n={},getFills:r}=this.props,i=(0,x.map)(r(t,this),(e=>{const t=(0,x.isFunction)(e.children)?e.children(n):e.children;return o.Children.map(t,((e,t)=>{if(!e||(0,x.isString)(e))return e;const n=e.key||t;return(0,o.cloneElement)(e,{key:n})}))})).filter((0,x.negate)(o.isEmptyElement));return(0,o.createElement)(o.Fragment,null,(0,x.isFunction)(e)?e(i):i)}}const S=e=>(0,o.createElement)(k.A.Consumer,null,(({registerSlot:t,unregisterSlot:n,getFills:i})=>(0,o.createElement)(T,(0,r.A)({},e,{registerSlot:t,unregisterSlot:n,getFills:i}))));var A=n(9364),C=n(8493);const O=(0,o.forwardRef)((function({name:e,fillProps:t={},as:n="div",...i},s){const a=(0,o.useContext)(C.A),l=(0,o.useRef)();return(0,o.useLayoutEffect)((()=>(a.registerSlot(e,l,t),()=>{a.unregisterSlot(e,l)})),[a.registerSlot,a.unregisterSlot,e]),(0,o.useLayoutEffect)((()=>{a.updateSlot(e,t)})),(0,o.createElement)(n,(0,r.A)({ref:(0,u.useMergeRefs)([s,l])},i))}));function N(e){return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(E.A,e),(0,o.createElement)(A.A,e))}const I=(0,o.forwardRef)((({bubblesVirtually:e,...t},n)=>e?(0,o.createElement)(O,(0,r.A)({},t,{ref:n})):(0,o.createElement)(S,t)));function _(e){return"appear"===e?"top":"left"}const L="Popover";function M(e,t){const{paddingTop:n,paddingBottom:r,paddingLeft:o,paddingRight:i}=(s=t).ownerDocument.defaultView.getComputedStyle(s);var s;const a=n?parseInt(n,10):0,l=r?parseInt(r,10):0,c=o?parseInt(o,10):0,u=i?parseInt(i,10):0;return{x:e.left+c,y:e.top+a,width:e.width-c-u,height:e.height-a-l,left:e.left+c,right:e.right-u,top:e.top+a,bottom:e.bottom-l}}function D(e,t,n){n?e.getAttribute(t)!==n&&e.setAttribute(t,n):e.hasAttribute(t)&&e.removeAttribute(t)}function R(e,t,n=""){e.style[t]!==n&&(e.style[t]=n)}function P(e,t,n){n?e.classList.contains(t)||e.classList.add(t):e.classList.contains(t)&&e.classList.remove(t)}const F=(0,o.forwardRef)((({headerTitle:e,onClose:t,children:n,className:i,noArrow:l=!0,isAlternate:p,position:g="bottom right",range:v,focusOnMount:y="firstElement",anchorRef:E,shouldAnchorIncludePadding:x,anchorRect:k,getAnchorRect:T,expandOnMobile:S,animate:A=!0,onClickOutside:C,onFocusOutside:O,__unstableStickyBoundaryElement:I,__unstableSlotName:F=L,__unstableObserveElement:j,__unstableBoundaryParent:B,__unstableForcePosition:H,__unstableForceXAlignment:z,...V},W)=>{const U=(0,o.useRef)(null),$=(0,o.useRef)(null),q=(0,o.useRef)(),K=(0,u.useViewportMatch)("medium","<"),[G,Y]=(0,o.useState)(),X=(0,w.A)(F),Z=S&&K,[Q,J]=(0,u.useResizeObserver)();l=Z||l,(0,o.useLayoutEffect)((()=>{if(Z)return P(q.current,"is-without-arrow",l),P(q.current,"is-alternate",p),D(q.current,"data-x-axis"),D(q.current,"data-y-axis"),R(q.current,"top"),R(q.current,"left"),R($.current,"maxHeight"),void R($.current,"maxWidth");const e=()=>{if(!q.current||!$.current)return;let e=function(e,t,n,r=!1,o,i){if(t)return t;if(n){if(!e.current)return;const t=n(e.current);return m(t,t.ownerDocument||e.current.ownerDocument,i)}if(!1!==r){if(!(r&&window.Range&&window.Element&&window.DOMRect))return;if("function"==typeof(null==r?void 0:r.cloneRange))return m((0,a.getRectangleFromRange)(r),r.endContainer.ownerDocument,i);if("function"==typeof(null==r?void 0:r.getBoundingClientRect)){const e=m(r.getBoundingClientRect(),r.ownerDocument,i);return o?e:M(e,r)}const{top:e,bottom:t}=r,n=e.getBoundingClientRect(),s=t.getBoundingClientRect(),l=m(new window.DOMRect(n.left,n.top,n.width,s.bottom-n.top),e.ownerDocument,i);return o?l:M(l,r)}if(!e.current)return;const{parentNode:s}=e.current,l=s.getBoundingClientRect();return o?l:M(l,s)}(U,k,T,E,x,q.current);if(!e)return;const{offsetParent:t,ownerDocument:n}=q.current;let r,o=0;if(t&&t!==n.body){const n=t.getBoundingClientRect();o=n.top,e=new window.DOMRect(e.left-n.left,e.top-n.top,e.width,e.height)}var i;B&&(r=null===(i=q.current.closest(".popover-slot"))||void 0===i?void 0:i.parentNode);const s=J.height?J:$.current.getBoundingClientRect(),{popoverTop:c,popoverLeft:u,xAxis:d,yAxis:h,contentHeight:v,contentWidth:y}=function(e,t,n="top",r,o,i,s,a,l){const[c,u="center",p]=n.split(" "),d=function(e,t,n,r,o,i,s,a){const{height:l}=t;if(o){const t=o.getBoundingClientRect().top+l-s;if(e.top<=t)return{yAxis:n,popoverTop:Math.min(e.bottom,t)}}let c=e.top+e.height/2;"bottom"===r?c=e.bottom:"top"===r&&(c=e.top);const u={popoverTop:c,contentHeight:(c-l/2>0?l/2:c)+(c+l/2>window.innerHeight?window.innerHeight-c:l/2)},p={popoverTop:e.top,contentHeight:e.top-10-l>0?l:e.top-10},d={popoverTop:e.bottom,contentHeight:e.bottom+10+l>window.innerHeight?window.innerHeight-10-e.bottom:l};let f,m=n,h=null;if(!o&&!a)if("middle"===n&&u.contentHeight===l)m="middle";else if("top"===n&&p.contentHeight===l)m="top";else if("bottom"===n&&d.contentHeight===l)m="bottom";else{m=p.contentHeight>d.contentHeight?"top":"bottom";const e="top"===m?p.contentHeight:d.contentHeight;h=e!==l?e:null}return f="middle"===m?u.popoverTop:"top"===m?p.popoverTop:d.popoverTop,{yAxis:m,popoverTop:f,contentHeight:h}}(e,t,c,p,r,0,i,a),m=function(e,t,n,r,o,i,s,a,l){const{width:c}=t;"left"===n&&(0,f.isRTL)()?n="right":"right"===n&&(0,f.isRTL)()&&(n="left"),"left"===r&&(0,f.isRTL)()?r="right":"right"===r&&(0,f.isRTL)()&&(r="left");const u=Math.round(e.left+e.width/2),p={popoverLeft:u,contentWidth:(u-c/2>0?c/2:u)+(u+c/2>window.innerWidth?window.innerWidth-u:c/2)};let d=e.left;"right"===r?d=e.right:"middle"===i||l||(d=u);let m=e.right;"left"===r?m=e.left:"middle"===i||l||(m=u);const h={popoverLeft:d,contentWidth:d-c>0?c:d},g={popoverLeft:m,contentWidth:m+c>window.innerWidth?window.innerWidth-m:c};let v,y=n,b=null;if(!o&&!a)if("center"===n&&p.contentWidth===c)y="center";else if("left"===n&&h.contentWidth===c)y="left";else if("right"===n&&g.contentWidth===c)y="right";else{y=h.contentWidth>g.contentWidth?"left":"right";const e="left"===y?h.contentWidth:g.contentWidth;c>window.innerWidth&&(b=window.innerWidth),e!==c&&(y="center",p.popoverLeft=window.innerWidth/2)}if(v="center"===y?p.popoverLeft:"left"===y?h.popoverLeft:g.popoverLeft,s){const e=s.getBoundingClientRect();v=Math.min(v,e.right-c),(0,f.isRTL)()||(v=Math.max(v,0))}return{xAxis:y,popoverLeft:v,contentWidth:b}}(e,t,u,p,r,d.yAxis,s,a,l);return{...m,...d}}(e,s,g,I,q.current,o,r,H,z);"number"==typeof c&&"number"==typeof u&&(R(q.current,"top",c+"px"),R(q.current,"left",u+"px")),P(q.current,"is-without-arrow",l||"center"===d&&"middle"===h),P(q.current,"is-alternate",p),D(q.current,"data-x-axis",d),D(q.current,"data-y-axis",h),R($.current,"maxHeight","number"==typeof v?v+"px":""),R($.current,"maxWidth","number"==typeof y?y+"px":""),Y(({left:"right",right:"left"}[d]||"center")+" "+({top:"bottom",bottom:"top"}[h]||"middle"))};e();const{ownerDocument:t}=q.current,{defaultView:n}=t,r=n.setInterval(e,500);let o;const i=()=>{n.cancelAnimationFrame(o),o=n.requestAnimationFrame(e)};n.addEventListener("click",i),n.addEventListener("resize",e),n.addEventListener("scroll",e,!0);const s=function(e){if(e)return e.endContainer?e.endContainer.ownerDocument:e.top?e.top.ownerDocument:e.ownerDocument}(E);let c;return s&&s!==t&&(s.defaultView.addEventListener("resize",e),s.defaultView.addEventListener("scroll",e,!0)),j&&(c=new n.MutationObserver(e),c.observe(j,{attributes:!0})),()=>{n.clearInterval(r),n.removeEventListener("resize",e),n.removeEventListener("scroll",e,!0),n.removeEventListener("click",i),n.cancelAnimationFrame(o),s&&s!==t&&(s.defaultView.removeEventListener("resize",e),s.defaultView.removeEventListener("scroll",e,!0)),c&&c.disconnect()}}),[Z,k,T,E,x,g,J,I,j,B]);const ee=(e,n)=>{if("focus-outside"===e&&O)O(n);else if("focus-outside"===e&&C){const e=new window.MouseEvent("click");Object.defineProperty(e,"target",{get:()=>n.relatedTarget}),c()("Popover onClickOutside prop",{since:"5.3",alternative:"onFocusOutside"}),C(e)}else t&&t()},[te,ne]=(0,u.__experimentalUseDialog)({focusOnMount:y,__unstableOnClose:ee,onClose:ee}),re=(0,u.useMergeRefs)([q,te,W]),oe=Boolean(A&&G)&&function(e){if("loading"===e.type)return s()("components-animate__loading");const{type:t,origin:n=_(t)}=e;if("appear"===t){const[e,t="center"]=n.split(" ");return s()("components-animate__appear",{["is-from-"+t]:"center"!==t,["is-from-"+e]:"middle"!==e})}return"slide-in"===t?s()("components-animate__slide-in","is-from-"+n):void 0}({type:"appear",origin:G});let ie=(0,o.createElement)("div",(0,r.A)({className:s()("components-popover",i,oe,{"is-expanded":Z,"is-without-arrow":l,"is-alternate":p})},V,{ref:re},ne,{tabIndex:"-1"}),Z&&(0,o.createElement)(b,null),Z&&(0,o.createElement)("div",{className:"components-popover__header"},(0,o.createElement)("span",{className:"components-popover__header-title"},e),(0,o.createElement)(h.A,{className:"components-popover__close",icon:d,onClick:t})),(0,o.createElement)("div",{ref:$,className:"components-popover__content"},(0,o.createElement)("div",{style:{position:"relative"}},Q,n)));return X.ref&&(ie=(0,o.createElement)(N,{name:F},ie)),E||k?ie:(0,o.createElement)("span",{ref:U},ie)}));F.Slot=(0,o.forwardRef)((function({name:e=L},t){return(0,o.createElement)(I,{bubblesVirtually:!0,name:e,className:"popover-slot",ref:t})}));const j=F},8596:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(6087),o=n(8468);const i=/^(432|454|819|915)$/.test(n.j)?function({shortcut:e,className:t}){if(!e)return null;let n,i;return(0,o.isString)(e)&&(n=e),(0,o.isObject)(e)&&(n=e.display,i=e.ariaLabel),(0,r.createElement)("span",{className:t,"aria-label":i},n)}:null},9364:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(6087);if(/^(432|454|819|915)$/.test(n.j))var o=n(5947);function i(){const[,e]=(0,r.useState)({}),t=(0,r.useRef)(!0);return(0,r.useEffect)((()=>()=>{t.current=!1}),[]),()=>{t.current&&e({})}}function s({name:e,children:t}){const n=(0,o.A)(e),s=(0,r.useRef)({rerender:i()});return(0,r.useEffect)((()=>(n.registerFill(s),()=>{n.unregisterFill(s)})),[n.registerFill,n.unregisterFill]),n.ref&&n.ref.current?("function"==typeof t&&(t=t(n.fillProps)),(0,r.createPortal)(t,n.ref.current)):null}},8493:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(6087);n(979);const o=(0,r.createContext)({slots:{},fills:{},registerSlot:()=>{"undefined"!=typeof process&&process.env},updateSlot:()=>{},unregisterSlot:()=>{},registerFill:()=>{},unregisterFill:()=>{}}),i=/^(432|454|819|915)$/.test(n.j)?o:null},5947:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(6087);if(/^(432|454|819|915)$/.test(n.j))var o=n(8493);function i(e){const t=(0,r.useContext)(o.A),n=t.slots[e]||{},i=t.fills[e],s=(0,r.useMemo)((()=>i||[]),[i]);return{...n,updateSlot:(0,r.useCallback)((n=>{t.updateSlot(e,n)}),[e,t.updateSlot]),unregisterSlot:(0,r.useCallback)((n=>{t.unregisterSlot(e,n)}),[e,t.unregisterSlot]),fills:s,registerFill:(0,r.useCallback)((n=>{t.registerFill(e,n)}),[e,t.registerFill]),unregisterFill:(0,r.useCallback)((n=>{t.unregisterFill(e,n)}),[e,t.unregisterFill])}}},4855:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const r=(0,n(6087).createContext)({registerSlot:()=>{},unregisterSlot:()=>{},registerFill:()=>{},unregisterFill:()=>{},getSlot:()=>{},getFills:()=>{},subscribe:()=>{}}),o=/^(432|454|819|915)$/.test(n.j)?r:null},5291:(e,t,n)=>{"use strict";if(n.d(t,{A:()=>c}),/^(432|454|819|915)$/.test(n.j))var r=n(2509);var o=n(6087),i=n(8468);if(/^(432|454|819|915)$/.test(n.j))var s=n(4855);if(/^(432|454|819|915)$/.test(n.j))var a=n(4120);function l({name:e,children:t,registerFill:n,unregisterFill:r}){const s=(0,a.A)(e),l=(0,o.useRef)({name:e,children:t});return(0,o.useLayoutEffect)((()=>(n(e,l.current),()=>r(e,l.current))),[]),(0,o.useLayoutEffect)((()=>{l.current.children=t,s&&s.forceUpdate()}),[t]),(0,o.useLayoutEffect)((()=>{e!==l.current.name&&(r(l.current.name,l.current),l.current.name=e,n(e,l.current))}),[e]),s&&s.node?((0,i.isFunction)(t)&&(t=t(s.props.fillProps)),(0,o.createPortal)(t,s.node)):null}const c=/^(432|454|819|915)$/.test(n.j)?e=>(0,o.createElement)(s.A.Consumer,null,(({registerFill:t,unregisterFill:n})=>(0,o.createElement)(l,(0,r.A)({},e,{registerFill:t,unregisterFill:n})))):null},4120:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(6087);if(/^(432|454|819|915)$/.test(n.j))var o=n(4855);const i=/^(432|454|819|915)$/.test(n.j)?e=>{const{getSlot:t,subscribe:n}=(0,r.useContext)(o.A),[i,s]=(0,r.useState)(t(e));return(0,r.useEffect)((()=>(s(t(e)),n((()=>{s(t(e))})))),[e]),i}:null},606:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(7697),o=n.n(r),i=n(6087);const s=(0,i.forwardRef)((function({as:e="div",className:t,...n},r){return function({as:e="div",...t}){return"function"==typeof t.children?t.children(t):(0,i.createElement)(e,t)}({as:e,className:o()("components-visually-hidden",t),...n,ref:r})}))},5521:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(6087),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})),s=/^(432|454|819|915)$/.test(n.j)?i:null},4530:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(6087);const o=(0,r.forwardRef)((function({icon:e,size:t=24,...n},o){return(0,r.cloneElement)(e,{width:t,height:t,...n,ref:o})}))},2174:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})),s=/^(432|454|819|915)$/.test(n.j)?i:null},1924:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})),s=/^(507|819)$/.test(n.j)?i:null},428:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M13 5c-3.3 0-6 2.7-6 6 0 1.4.5 2.7 1.3 3.7l-3.8 3.8 1.1 1.1 3.8-3.8c1 .8 2.3 1.3 3.7 1.3 3.3 0 6-2.7 6-6S16.3 5 13 5zm0 10.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5z"})),s=1==n.j?i:null},2098:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24"},(0,r.createElement)(o.Path,{d:"M10 2c4.42 0 8 3.58 8 8s-3.58 8-8 8-8-3.58-8-8 3.58-8 8-8zm1.13 9.38l.35-6.46H8.52l.35 6.46h2.26zm-.09 3.36c.24-.23.37-.55.37-.96 0-.42-.12-.74-.36-.97s-.59-.35-1.06-.35-.82.12-1.07.35-.37.55-.37.97c0 .41.13.73.38.96.26.23.61.34 1.06.34s.8-.11 1.05-.34z"})),s=1==n.j?i:null},7697:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var s=o.apply(null,n);s&&e.push(s)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var a in n)r.call(n,a)&&n[a]&&e.push(a)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},9571:(e,t,n)=>{"use strict";var r=n(2984);e.exports=function(e,t,n){n=n||{},9===t.nodeType&&(t=r.getWindow(t));var o=n.allowHorizontalScroll,i=n.onlyScrollIfNeeded,s=n.alignWithTop,a=n.alignWithLeft,l=n.offsetTop||0,c=n.offsetLeft||0,u=n.offsetBottom||0,p=n.offsetRight||0;o=void 0===o||o;var d=r.isWindow(t),f=r.offset(e),m=r.outerHeight(e),h=r.outerWidth(e),g=void 0,v=void 0,y=void 0,b=void 0,w=void 0,E=void 0,x=void 0,k=void 0,T=void 0,S=void 0;d?(x=t,S=r.height(x),T=r.width(x),k={left:r.scrollLeft(x),top:r.scrollTop(x)},w={left:f.left-k.left-c,top:f.top-k.top-l},E={left:f.left+h-(k.left+T)+p,top:f.top+m-(k.top+S)+u},b=k):(g=r.offset(t),v=t.clientHeight,y=t.clientWidth,b={left:t.scrollLeft,top:t.scrollTop},w={left:f.left-(g.left+(parseFloat(r.css(t,"borderLeftWidth"))||0))-c,top:f.top-(g.top+(parseFloat(r.css(t,"borderTopWidth"))||0))-l},E={left:f.left+h-(g.left+y+(parseFloat(r.css(t,"borderRightWidth"))||0))+p,top:f.top+m-(g.top+v+(parseFloat(r.css(t,"borderBottomWidth"))||0))+u}),w.top<0||E.top>0?!0===s?r.scrollTop(t,b.top+w.top):!1===s?r.scrollTop(t,b.top+E.top):w.top<0?r.scrollTop(t,b.top+w.top):r.scrollTop(t,b.top+E.top):i||((s=void 0===s||!!s)?r.scrollTop(t,b.top+w.top):r.scrollTop(t,b.top+E.top)),o&&(w.left<0||E.left>0?!0===a?r.scrollLeft(t,b.left+w.left):!1===a?r.scrollLeft(t,b.left+E.left):w.left<0?r.scrollLeft(t,b.left+w.left):r.scrollLeft(t,b.left+E.left):i||((a=void 0===a||!!a)?r.scrollLeft(t,b.left+w.left):r.scrollLeft(t,b.left+E.left)))}},6406:(e,t,n)=>{"use strict";e.exports=n(9571)},2984:e=>{"use strict";var t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};function r(e,t){var n=e["page"+(t?"Y":"X")+"Offset"],r="scroll"+(t?"Top":"Left");if("number"!=typeof n){var o=e.document;"number"!=typeof(n=o.documentElement[r])&&(n=o.body[r])}return n}function o(e){return r(e)}function i(e){return r(e,!0)}function s(e){var t=function(e){var t,n=void 0,r=void 0,o=e.ownerDocument,i=o.body,s=o&&o.documentElement;return n=(t=e.getBoundingClientRect()).left,r=t.top,{left:n-=s.clientLeft||i.clientLeft||0,top:r-=s.clientTop||i.clientTop||0}}(e),n=e.ownerDocument,r=n.defaultView||n.parentWindow;return t.left+=o(r),t.top+=i(r),t}var a=new RegExp("^("+/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source+")(?!px)[a-z%]+$","i"),l=/^(top|right|bottom|left)$/,c="currentStyle",u="runtimeStyle",p="left",d=void 0;function f(e,t){for(var n=0;n<e.length;n++)t(e[n])}function m(e){return"border-box"===d(e,"boxSizing")}"undefined"!=typeof window&&(d=window.getComputedStyle?function(e,t,n){var r="",o=e.ownerDocument,i=n||o.defaultView.getComputedStyle(e,null);return i&&(r=i.getPropertyValue(t)||i[t]),r}:function(e,t){var n=e[c]&&e[c][t];if(a.test(n)&&!l.test(t)){var r=e.style,o=r[p],i=e[u][p];e[u][p]=e[c][p],r[p]="fontSize"===t?"1em":n||0,n=r.pixelLeft+"px",r[p]=o,e[u][p]=i}return""===n?"auto":n});var h=["margin","border","padding"];function g(e,t,n){var r=0,o=void 0,i=void 0,s=void 0;for(i=0;i<t.length;i++)if(o=t[i])for(s=0;s<n.length;s++){var a;a="border"===o?o+n[s]+"Width":o+n[s],r+=parseFloat(d(e,a))||0}return r}function v(e){return null!=e&&e==e.window}var y={};function b(e,t,n){if(v(e))return"width"===t?y.viewportWidth(e):y.viewportHeight(e);if(9===e.nodeType)return"width"===t?y.docWidth(e):y.docHeight(e);var r="width"===t?["Left","Right"]:["Top","Bottom"],o="width"===t?e.offsetWidth:e.offsetHeight,i=(d(e),m(e)),s=0;(null==o||o<=0)&&(o=void 0,(null==(s=d(e,t))||Number(s)<0)&&(s=e.style[t]||0),s=parseFloat(s)||0),void 0===n&&(n=i?1:-1);var a=void 0!==o||i,l=o||s;if(-1===n)return a?l-g(e,["border","padding"],r):s;if(a){var c=2===n?-g(e,["border"],r):g(e,["margin"],r);return l+(1===n?0:c)}return s+g(e,h.slice(n),r)}f(["Width","Height"],(function(e){y["doc"+e]=function(t){var n=t.document;return Math.max(n.documentElement["scroll"+e],n.body["scroll"+e],y["viewport"+e](n))},y["viewport"+e]=function(t){var n="client"+e,r=t.document,o=r.body,i=r.documentElement[n];return"CSS1Compat"===r.compatMode&&i||o&&o[n]||i}}));var w={position:"absolute",visibility:"hidden",display:"block"};function E(e){var t=void 0,n=arguments;return 0!==e.offsetWidth?t=b.apply(void 0,n):function(e,r){var o={},i=e.style,s=void 0;for(s in r)r.hasOwnProperty(s)&&(o[s]=i[s],i[s]=r[s]);for(s in function(){t=b.apply(void 0,n)}.call(e),r)r.hasOwnProperty(s)&&(i[s]=o[s])}(e,w),t}function x(e,t,r){var o=r;if("object"!==(void 0===t?"undefined":n(t)))return void 0!==o?("number"==typeof o&&(o+="px"),void(e.style[t]=o)):d(e,t);for(var i in t)t.hasOwnProperty(i)&&x(e,i,t[i])}f(["width","height"],(function(e){var t=e.charAt(0).toUpperCase()+e.slice(1);y["outer"+t]=function(t,n){return t&&E(t,e,n?0:1)};var n="width"===e?["Left","Right"]:["Top","Bottom"];y[e]=function(t,r){return void 0===r?t&&E(t,e,-1):t?(d(t),m(t)&&(r+=g(t,["padding","border"],n)),x(t,e,r)):void 0}})),e.exports=t({getWindow:function(e){var t=e.ownerDocument||e;return t.defaultView||t.parentWindow},offset:function(e,t){if(void 0===t)return s(e);!function(e,t){"static"===x(e,"position")&&(e.style.position="relative");var n=s(e),r={},o=void 0,i=void 0;for(i in t)t.hasOwnProperty(i)&&(o=parseFloat(x(e,i))||0,r[i]=o+t[i]-n[i]);x(e,r)}(e,t)},isWindow:v,each:f,css:x,clone:function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);if(e.overflow)for(var n in e)e.hasOwnProperty(n)&&(t.overflow[n]=e.overflow[n]);return t},scrollLeft:function(e,t){if(v(e)){if(void 0===t)return o(e);window.scrollTo(t,i(e))}else{if(void 0===t)return e.scrollLeft;e.scrollLeft=t}},scrollTop:function(e,t){if(v(e)){if(void 0===t)return i(e);window.scrollTo(o(e),t)}else{if(void 0===t)return e.scrollTop;e.scrollTop=t}},viewportWidth:0,viewportHeight:0},y)},1487:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Doctype=t.CDATA=t.Tag=t.Style=t.Script=t.Comment=t.Directive=t.Text=t.Root=t.isTag=t.ElementType=void 0,function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(n=t.ElementType||(t.ElementType={})),t.isTag=function(e){return e.type===n.Tag||e.type===n.Script||e.type===n.Style},t.Root=n.Root,t.Text=n.Text,t.Directive=n.Directive,t.Comment=n.Comment,t.Script=n.Script,t.Style=n.Style,t.Tag=n.Tag,t.CDATA=n.CDATA,t.Doctype=n.Doctype},5946:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.DomHandler=void 0;var i=n(1487),s=n(1456);o(n(1456),t);var a={withStartIndices:!1,withEndIndices:!1,xmlMode:!1},l=function(){function e(e,t,n){this.dom=[],this.root=new s.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(n=t,t=a),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:a,this.elementCB=null!=n?n:null}return e.prototype.onparserinit=function(e){this.parser=e},e.prototype.onreset=function(){this.dom=[],this.root=new s.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},e.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},e.prototype.onerror=function(e){this.handleCallback(e)},e.prototype.onclosetag=function(){this.lastNode=null;var e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)},e.prototype.onopentag=function(e,t){var n=this.options.xmlMode?i.ElementType.Tag:void 0,r=new s.Element(e,t,void 0,n);this.addNode(r),this.tagStack.push(r)},e.prototype.ontext=function(e){var t=this.lastNode;if(t&&t.type===i.ElementType.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{var n=new s.Text(e);this.addNode(n),this.lastNode=n}},e.prototype.oncomment=function(e){if(this.lastNode&&this.lastNode.type===i.ElementType.Comment)this.lastNode.data+=e;else{var t=new s.Comment(e);this.addNode(t),this.lastNode=t}},e.prototype.oncommentend=function(){this.lastNode=null},e.prototype.oncdatastart=function(){var e=new s.Text(""),t=new s.CDATA([e]);this.addNode(t),e.parent=t,this.lastNode=e},e.prototype.oncdataend=function(){this.lastNode=null},e.prototype.onprocessinginstruction=function(e,t){var n=new s.ProcessingInstruction(e,t);this.addNode(n)},e.prototype.handleCallback=function(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e},e.prototype.addNode=function(e){var t=this.tagStack[this.tagStack.length-1],n=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),n&&(e.prev=n,n.next=e),e.parent=t,this.lastNode=null},e}();t.DomHandler=l,t.default=l},1456:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}),i=this&&this.__assign||function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.cloneNode=t.hasChildren=t.isDocument=t.isDirective=t.isComment=t.isText=t.isCDATA=t.isTag=t.Element=t.Document=t.CDATA=t.NodeWithChildren=t.ProcessingInstruction=t.Comment=t.Text=t.DataNode=t.Node=void 0;var s=n(1487),a=function(){function e(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.parent},set:function(e){this.parent=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"previousSibling",{get:function(){return this.prev},set:function(e){this.prev=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"nextSibling",{get:function(){return this.next},set:function(e){this.next=e},enumerable:!1,configurable:!0}),e.prototype.cloneNode=function(e){return void 0===e&&(e=!1),x(this,e)},e}();t.Node=a;var l=function(e){function t(t){var n=e.call(this)||this;return n.data=t,n}return o(t,e),Object.defineProperty(t.prototype,"nodeValue",{get:function(){return this.data},set:function(e){this.data=e},enumerable:!1,configurable:!0}),t}(a);t.DataNode=l;var c=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.Text,t}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 3},enumerable:!1,configurable:!0}),t}(l);t.Text=c;var u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.Comment,t}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 8},enumerable:!1,configurable:!0}),t}(l);t.Comment=u;var p=function(e){function t(t,n){var r=e.call(this,n)||this;return r.name=t,r.type=s.ElementType.Directive,r}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),t}(l);t.ProcessingInstruction=p;var d=function(e){function t(t){var n=e.call(this)||this;return n.children=t,n}return o(t,e),Object.defineProperty(t.prototype,"firstChild",{get:function(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.children},set:function(e){this.children=e},enumerable:!1,configurable:!0}),t}(a);t.NodeWithChildren=d;var f=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.CDATA,t}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 4},enumerable:!1,configurable:!0}),t}(d);t.CDATA=f;var m=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.Root,t}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 9},enumerable:!1,configurable:!0}),t}(d);t.Document=m;var h=function(e){function t(t,n,r,o){void 0===r&&(r=[]),void 0===o&&(o="script"===t?s.ElementType.Script:"style"===t?s.ElementType.Style:s.ElementType.Tag);var i=e.call(this,r)||this;return i.name=t,i.attribs=n,i.type=o,i}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tagName",{get:function(){return this.name},set:function(e){this.name=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){var e=this;return Object.keys(this.attribs).map((function(t){var n,r;return{name:t,value:e.attribs[t],namespace:null===(n=e["x-attribsNamespace"])||void 0===n?void 0:n[t],prefix:null===(r=e["x-attribsPrefix"])||void 0===r?void 0:r[t]}}))},enumerable:!1,configurable:!0}),t}(d);function g(e){return(0,s.isTag)(e)}function v(e){return e.type===s.ElementType.CDATA}function y(e){return e.type===s.ElementType.Text}function b(e){return e.type===s.ElementType.Comment}function w(e){return e.type===s.ElementType.Directive}function E(e){return e.type===s.ElementType.Root}function x(e,t){var n;if(void 0===t&&(t=!1),y(e))n=new c(e.data);else if(b(e))n=new u(e.data);else if(g(e)){var r=t?k(e.children):[],o=new h(e.name,i({},e.attribs),r);r.forEach((function(e){return e.parent=o})),null!=e.namespace&&(o.namespace=e.namespace),e["x-attribsNamespace"]&&(o["x-attribsNamespace"]=i({},e["x-attribsNamespace"])),e["x-attribsPrefix"]&&(o["x-attribsPrefix"]=i({},e["x-attribsPrefix"])),n=o}else if(v(e)){r=t?k(e.children):[];var s=new f(r);r.forEach((function(e){return e.parent=s})),n=s}else if(E(e)){r=t?k(e.children):[];var a=new m(r);r.forEach((function(e){return e.parent=a})),e["x-mode"]&&(a["x-mode"]=e["x-mode"]),n=a}else{if(!w(e))throw new Error("Not implemented yet: ".concat(e.type));var l=new p(e.name,e.data);null!=e["x-name"]&&(l["x-name"]=e["x-name"],l["x-publicId"]=e["x-publicId"],l["x-systemId"]=e["x-systemId"]),n=l}return n.startIndex=e.startIndex,n.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(n.sourceCodeLocation=e.sourceCodeLocation),n}function k(e){for(var t=e.map((function(e){return x(e,!0)})),n=1;n<t.length;n++)t[n].prev=t[n-1],t[n-1].next=t[n];return t}t.Element=h,t.isTag=g,t.isCDATA=v,t.isText=y,t.isComment=b,t.isDirective=w,t.isDocument=E,t.hasChildren=function(e){return Object.prototype.hasOwnProperty.call(e,"children")},t.cloneNode=x},3240:function(e){e.exports=function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,n){return t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},t(e,n)}function n(e,r,o){return n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,n,r){var o=[null];o.push.apply(o,n);var i=new(Function.bind.apply(e,o));return r&&t(i,r.prototype),i},n.apply(null,arguments)}function r(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var i=Object.hasOwnProperty,s=Object.setPrototypeOf,a=Object.isFrozen,l=Object.getPrototypeOf,c=Object.getOwnPropertyDescriptor,u=Object.freeze,p=Object.seal,d=Object.create,f="undefined"!=typeof Reflect&&Reflect,m=f.apply,h=f.construct;m||(m=function(e,t,n){return e.apply(t,n)}),u||(u=function(e){return e}),p||(p=function(e){return e}),h||(h=function(e,t){return n(e,r(t))});var g,v=O(Array.prototype.forEach),y=O(Array.prototype.pop),b=O(Array.prototype.push),w=O(String.prototype.toLowerCase),E=O(String.prototype.toString),x=O(String.prototype.match),k=O(String.prototype.replace),T=O(String.prototype.indexOf),S=O(String.prototype.trim),A=O(RegExp.prototype.test),C=(g=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return h(g,t)});function O(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return m(e,t,r)}}function N(e,t,n){var r;n=null!==(r=n)&&void 0!==r?r:w,s&&s(e,null);for(var o=t.length;o--;){var i=t[o];if("string"==typeof i){var l=n(i);l!==i&&(a(t)||(t[o]=l),i=l)}e[i]=!0}return e}function I(e){var t,n=d(null);for(t in e)!0===m(i,e,[t])&&(n[t]=e[t]);return n}function _(e,t){for(;null!==e;){var n=c(e,t);if(n){if(n.get)return O(n.get);if("function"==typeof n.value)return O(n.value)}e=l(e)}return function(e){return console.warn("fallback value for",e),null}}var L=u(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),M=u(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),D=u(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),R=u(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),P=u(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),F=u(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),j=u(["#text"]),B=u(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),H=u(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),z=u(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),V=u(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),W=p(/\{\{[\w\W]*|[\w\W]*\}\}/gm),U=p(/<%[\w\W]*|[\w\W]*%>/gm),$=p(/\${[\w\W]*}/gm),q=p(/^data-[\-\w.\u00B7-\uFFFF]/),K=p(/^aria-[\-\w]+$/),G=p(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Y=p(/^(?:\w+script|data):/i),X=p(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Z=p(/^html$/i),Q=p(/^[a-z][.\w]*(-[.\w]+)+$/i),J=function(){return"undefined"==typeof window?null:window};return function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:J(),o=function(e){return t(e)};if(o.version="2.5.7",o.removed=[],!n||!n.document||9!==n.document.nodeType)return o.isSupported=!1,o;var i=n.document,s=n.document,a=n.DocumentFragment,l=n.HTMLTemplateElement,c=n.Node,p=n.Element,d=n.NodeFilter,f=n.NamedNodeMap,m=void 0===f?n.NamedNodeMap||n.MozNamedAttrMap:f,h=n.HTMLFormElement,g=n.DOMParser,O=n.trustedTypes,ee=p.prototype,te=_(ee,"cloneNode"),ne=_(ee,"nextSibling"),re=_(ee,"childNodes"),oe=_(ee,"parentNode");if("function"==typeof l){var ie=s.createElement("template");ie.content&&ie.content.ownerDocument&&(s=ie.content.ownerDocument)}var se=function(t,n){if("object"!==e(t)||"function"!=typeof t.createPolicy)return null;var r=null,o="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(o)&&(r=n.currentScript.getAttribute(o));var i="dompurify"+(r?"#"+r:"");try{return t.createPolicy(i,{createHTML:function(e){return e},createScriptURL:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+i+" could not be created."),null}}(O,i),ae=se?se.createHTML(""):"",le=s,ce=le.implementation,ue=le.createNodeIterator,pe=le.createDocumentFragment,de=le.getElementsByTagName,fe=i.importNode,me={};try{me=I(s).documentMode?s.documentMode:{}}catch(e){}var he={};o.isSupported="function"==typeof oe&&ce&&void 0!==ce.createHTMLDocument&&9!==me;var ge,ve,ye=W,be=U,we=$,Ee=q,xe=K,ke=Y,Te=X,Se=Q,Ae=G,Ce=null,Oe=N({},[].concat(r(L),r(M),r(D),r(P),r(j))),Ne=null,Ie=N({},[].concat(r(B),r(H),r(z),r(V))),_e=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Le=null,Me=null,De=!0,Re=!0,Pe=!1,Fe=!0,je=!1,Be=!0,He=!1,ze=!1,Ve=!1,We=!1,Ue=!1,$e=!1,qe=!0,Ke=!1,Ge=!0,Ye=!1,Xe={},Ze=null,Qe=N({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Je=null,et=N({},["audio","video","img","source","image","track"]),tt=null,nt=N({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),rt="http://www.w3.org/1998/Math/MathML",ot="http://www.w3.org/2000/svg",it="http://www.w3.org/1999/xhtml",st=it,at=!1,lt=null,ct=N({},[rt,ot,it],E),ut=["application/xhtml+xml","text/html"],pt=null,dt=s.createElement("form"),ft=function(e){return e instanceof RegExp||e instanceof Function},mt=function(t){pt&&pt===t||(t&&"object"===e(t)||(t={}),t=I(t),ge=ge=-1===ut.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,ve="application/xhtml+xml"===ge?E:w,Ce="ALLOWED_TAGS"in t?N({},t.ALLOWED_TAGS,ve):Oe,Ne="ALLOWED_ATTR"in t?N({},t.ALLOWED_ATTR,ve):Ie,lt="ALLOWED_NAMESPACES"in t?N({},t.ALLOWED_NAMESPACES,E):ct,tt="ADD_URI_SAFE_ATTR"in t?N(I(nt),t.ADD_URI_SAFE_ATTR,ve):nt,Je="ADD_DATA_URI_TAGS"in t?N(I(et),t.ADD_DATA_URI_TAGS,ve):et,Ze="FORBID_CONTENTS"in t?N({},t.FORBID_CONTENTS,ve):Qe,Le="FORBID_TAGS"in t?N({},t.FORBID_TAGS,ve):{},Me="FORBID_ATTR"in t?N({},t.FORBID_ATTR,ve):{},Xe="USE_PROFILES"in t&&t.USE_PROFILES,De=!1!==t.ALLOW_ARIA_ATTR,Re=!1!==t.ALLOW_DATA_ATTR,Pe=t.ALLOW_UNKNOWN_PROTOCOLS||!1,Fe=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,je=t.SAFE_FOR_TEMPLATES||!1,Be=!1!==t.SAFE_FOR_XML,He=t.WHOLE_DOCUMENT||!1,We=t.RETURN_DOM||!1,Ue=t.RETURN_DOM_FRAGMENT||!1,$e=t.RETURN_TRUSTED_TYPE||!1,Ve=t.FORCE_BODY||!1,qe=!1!==t.SANITIZE_DOM,Ke=t.SANITIZE_NAMED_PROPS||!1,Ge=!1!==t.KEEP_CONTENT,Ye=t.IN_PLACE||!1,Ae=t.ALLOWED_URI_REGEXP||Ae,st=t.NAMESPACE||it,_e=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&ft(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(_e.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&ft(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(_e.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(_e.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),je&&(Re=!1),Ue&&(We=!0),Xe&&(Ce=N({},r(j)),Ne=[],!0===Xe.html&&(N(Ce,L),N(Ne,B)),!0===Xe.svg&&(N(Ce,M),N(Ne,H),N(Ne,V)),!0===Xe.svgFilters&&(N(Ce,D),N(Ne,H),N(Ne,V)),!0===Xe.mathMl&&(N(Ce,P),N(Ne,z),N(Ne,V))),t.ADD_TAGS&&(Ce===Oe&&(Ce=I(Ce)),N(Ce,t.ADD_TAGS,ve)),t.ADD_ATTR&&(Ne===Ie&&(Ne=I(Ne)),N(Ne,t.ADD_ATTR,ve)),t.ADD_URI_SAFE_ATTR&&N(tt,t.ADD_URI_SAFE_ATTR,ve),t.FORBID_CONTENTS&&(Ze===Qe&&(Ze=I(Ze)),N(Ze,t.FORBID_CONTENTS,ve)),Ge&&(Ce["#text"]=!0),He&&N(Ce,["html","head","body"]),Ce.table&&(N(Ce,["tbody"]),delete Le.tbody),u&&u(t),pt=t)},ht=N({},["mi","mo","mn","ms","mtext"]),gt=N({},["annotation-xml"]),vt=N({},["title","style","font","a","script"]),yt=N({},M);N(yt,D),N(yt,R);var bt=N({},P);N(bt,F);var wt=function(e){b(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=ae}catch(t){e.remove()}}},Et=function(e,t){try{b(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){b(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!Ne[e])if(We||Ue)try{wt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},xt=function(e){var t,n;if(Ve)e="<remove></remove>"+e;else{var r=x(e,/^[\r\n\t ]+/);n=r&&r[0]}"application/xhtml+xml"===ge&&st===it&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var o=se?se.createHTML(e):e;if(st===it)try{t=(new g).parseFromString(o,ge)}catch(e){}if(!t||!t.documentElement){t=ce.createDocument(st,"template",null);try{t.documentElement.innerHTML=at?ae:o}catch(e){}}var i=t.body||t.documentElement;return e&&n&&i.insertBefore(s.createTextNode(n),i.childNodes[0]||null),st===it?de.call(t,He?"html":"body")[0]:He?t.documentElement:i},kt=function(e){return ue.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT|d.SHOW_PROCESSING_INSTRUCTION|d.SHOW_CDATA_SECTION,null,!1)},Tt=function(e){return e instanceof h&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof m)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},St=function(t){return"object"===e(c)?t instanceof c:t&&"object"===e(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},At=function(e,t,n){he[e]&&v(he[e],(function(e){e.call(o,t,n,pt)}))},Ct=function(e){var t;if(At("beforeSanitizeElements",e,null),Tt(e))return wt(e),!0;if(A(/[\u0080-\uFFFF]/,e.nodeName))return wt(e),!0;var n=ve(e.nodeName);if(At("uponSanitizeElement",e,{tagName:n,allowedTags:Ce}),e.hasChildNodes()&&!St(e.firstElementChild)&&(!St(e.content)||!St(e.content.firstElementChild))&&A(/<[/\w]/g,e.innerHTML)&&A(/<[/\w]/g,e.textContent))return wt(e),!0;if("select"===n&&A(/<template/i,e.innerHTML))return wt(e),!0;if(7===e.nodeType)return wt(e),!0;if(Be&&8===e.nodeType&&A(/<[/\w]/g,e.data))return wt(e),!0;if(!Ce[n]||Le[n]){if(!Le[n]&&Nt(n)){if(_e.tagNameCheck instanceof RegExp&&A(_e.tagNameCheck,n))return!1;if(_e.tagNameCheck instanceof Function&&_e.tagNameCheck(n))return!1}if(Ge&&!Ze[n]){var r=oe(e)||e.parentNode,i=re(e)||e.childNodes;if(i&&r)for(var s=i.length-1;s>=0;--s){var a=te(i[s],!0);a.__removalCount=(e.__removalCount||0)+1,r.insertBefore(a,ne(e))}}return wt(e),!0}return e instanceof p&&!function(e){var t=oe(e);t&&t.tagName||(t={namespaceURI:st,tagName:"template"});var n=w(e.tagName),r=w(t.tagName);return!!lt[e.namespaceURI]&&(e.namespaceURI===ot?t.namespaceURI===it?"svg"===n:t.namespaceURI===rt?"svg"===n&&("annotation-xml"===r||ht[r]):Boolean(yt[n]):e.namespaceURI===rt?t.namespaceURI===it?"math"===n:t.namespaceURI===ot?"math"===n&&gt[r]:Boolean(bt[n]):e.namespaceURI===it?!(t.namespaceURI===ot&&!gt[r])&&!(t.namespaceURI===rt&&!ht[r])&&!bt[n]&&(vt[n]||!yt[n]):!("application/xhtml+xml"!==ge||!lt[e.namespaceURI]))}(e)?(wt(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!A(/<\/no(script|embed|frames)/i,e.innerHTML)?(je&&3===e.nodeType&&(t=e.textContent,t=k(t,ye," "),t=k(t,be," "),t=k(t,we," "),e.textContent!==t&&(b(o.removed,{element:e.cloneNode()}),e.textContent=t)),At("afterSanitizeElements",e,null),!1):(wt(e),!0)},Ot=function(e,t,n){if(qe&&("id"===t||"name"===t)&&(n in s||n in dt))return!1;if(Re&&!Me[t]&&A(Ee,t));else if(De&&A(xe,t));else if(!Ne[t]||Me[t]){if(!(Nt(e)&&(_e.tagNameCheck instanceof RegExp&&A(_e.tagNameCheck,e)||_e.tagNameCheck instanceof Function&&_e.tagNameCheck(e))&&(_e.attributeNameCheck instanceof RegExp&&A(_e.attributeNameCheck,t)||_e.attributeNameCheck instanceof Function&&_e.attributeNameCheck(t))||"is"===t&&_e.allowCustomizedBuiltInElements&&(_e.tagNameCheck instanceof RegExp&&A(_e.tagNameCheck,n)||_e.tagNameCheck instanceof Function&&_e.tagNameCheck(n))))return!1}else if(tt[t]);else if(A(Ae,k(n,Te,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==T(n,"data:")||!Je[e])if(Pe&&!A(ke,k(n,Te,"")));else if(n)return!1;return!0},Nt=function(e){return"annotation-xml"!==e&&x(e,Se)},It=function(t){var n,r,i,s;At("beforeSanitizeAttributes",t,null);var a=t.attributes;if(a){var l={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Ne};for(s=a.length;s--;){var c=n=a[s],u=c.name,p=c.namespaceURI;if(r="value"===u?n.value:S(n.value),i=ve(u),l.attrName=i,l.attrValue=r,l.keepAttr=!0,l.forceKeepAttr=void 0,At("uponSanitizeAttribute",t,l),r=l.attrValue,!l.forceKeepAttr&&(Et(u,t),l.keepAttr))if(Fe||!A(/\/>/i,r)){je&&(r=k(r,ye," "),r=k(r,be," "),r=k(r,we," "));var d=ve(t.nodeName);if(Ot(d,i,r))if(!Ke||"id"!==i&&"name"!==i||(Et(u,t),r="user-content-"+r),Be&&A(/((--!?|])>)|<\/(style|title)/i,r))Et(u,t);else{if(se&&"object"===e(O)&&"function"==typeof O.getAttributeType)if(p);else switch(O.getAttributeType(d,i)){case"TrustedHTML":r=se.createHTML(r);break;case"TrustedScriptURL":r=se.createScriptURL(r)}try{p?t.setAttributeNS(p,u,r):t.setAttribute(u,r),Tt(t)?wt(t):y(o.removed)}catch(e){}}}else Et(u,t)}At("afterSanitizeAttributes",t,null)}},_t=function e(t){var n,r=kt(t);for(At("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)At("uponSanitizeShadowNode",n,null),Ct(n)||(n.content instanceof a&&e(n.content),It(n));At("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(t){var r,s,l,u,p,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((at=!t)&&(t="\x3c!--\x3e"),"string"!=typeof t&&!St(t)){if("function"!=typeof t.toString)throw C("toString is not a function");if("string"!=typeof(t=t.toString()))throw C("dirty is not a string, aborting")}if(!o.isSupported){if("object"===e(n.toStaticHTML)||"function"==typeof n.toStaticHTML){if("string"==typeof t)return n.toStaticHTML(t);if(St(t))return n.toStaticHTML(t.outerHTML)}return t}if(ze||mt(d),o.removed=[],"string"==typeof t&&(Ye=!1),Ye){if(t.nodeName){var f=ve(t.nodeName);if(!Ce[f]||Le[f])throw C("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof c)1===(s=(r=xt("\x3c!----\x3e")).ownerDocument.importNode(t,!0)).nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?r=s:r.appendChild(s);else{if(!We&&!je&&!He&&-1===t.indexOf("<"))return se&&$e?se.createHTML(t):t;if(!(r=xt(t)))return We?null:$e?ae:""}r&&Ve&&wt(r.firstChild);for(var m=kt(Ye?t:r);l=m.nextNode();)3===l.nodeType&&l===u||Ct(l)||(l.content instanceof a&&_t(l.content),It(l),u=l);if(u=null,Ye)return t;if(We){if(Ue)for(p=pe.call(r.ownerDocument);r.firstChild;)p.appendChild(r.firstChild);else p=r;return(Ne.shadowroot||Ne.shadowrootmod)&&(p=fe.call(i,p,!0)),p}var h=He?r.outerHTML:r.innerHTML;return He&&Ce["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&A(Z,r.ownerDocument.doctype.name)&&(h="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+h),je&&(h=k(h,ye," "),h=k(h,be," "),h=k(h,we," ")),se&&$e?se.createHTML(h):h},o.setConfig=function(e){mt(e),ze=!0},o.clearConfig=function(){pt=null,ze=!1},o.isValidAttribute=function(e,t,n){pt||mt({});var r=ve(e),o=ve(t);return Ot(r,o,n)},o.addHook=function(e,t){"function"==typeof t&&(he[e]=he[e]||[],b(he[e],t))},o.removeHook=function(e){if(he[e])return y(he[e])},o.removeHooks=function(e){he[e]&&(he[e]=[])},o.removeAllHooks=function(){he={}},o}()}()},4420:(e,t,n)=>{"use strict";if(n.d(t,{a:()=>i}),/^(1|819)$/.test(n.j))var r=n(5029);if(/^(1|819)$/.test(n.j))var o=n(4738);function i(e,t){return void 0===t&&(t={}),(0,o.W)(e,(0,r.Cl)({delimiter:"."},t))}},1824:e=>{"use strict";e.exports=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var r,o,i;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(o=r;0!=o--;)if(!e(t[o],n[o]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;for(o of t.entries())if(!e(o[1],n.get(o[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(o=r;0!=o--;)if(t[o]!==n[o])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(o=r;0!=o--;)if(!Object.prototype.hasOwnProperty.call(n,i[o]))return!1;for(o=r;0!=o--;){var s=i[o];if(!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n}},9003:(e,t)=>{t.CASE_SENSITIVE_TAG_NAMES=["animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussainBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","linearGradient","radialGradient","textPath"]},6177:e=>{var t="html",n="head",r="body",o=/<([a-zA-Z]+[0-9]?)/,i=/<head[^]*>/i,s=/<body[^]*>/i,a=function(){throw new Error("This browser does not support `document.implementation.createHTMLDocument`")},l=function(){throw new Error("This browser does not support `DOMParser.prototype.parseFromString`")},c="object"==typeof window&&window.DOMParser;if("function"==typeof c){var u=new c;a=l=function(e,t){return t&&(e="<"+t+">"+e+"</"+t+">"),u.parseFromString(e,"text/html")}}if("object"==typeof document&&document.implementation){var p=document.implementation.createHTMLDocument();a=function(e,t){return t?(p.documentElement.querySelector(t).innerHTML=e,p):(p.documentElement.innerHTML=e,p)}}var d,f="object"==typeof document?document.createElement("template"):{};f.content&&(d=function(e){return f.innerHTML=e,f.content.childNodes}),e.exports=function(e){var c,u,p,f,m=e.match(o);switch(m&&m[1]&&(c=m[1].toLowerCase()),c){case t:return u=l(e),i.test(e)||(p=u.querySelector(n))&&p.parentNode.removeChild(p),s.test(e)||(p=u.querySelector(r))&&p.parentNode.removeChild(p),u.querySelectorAll(t);case n:case r:return f=(u=a(e)).querySelectorAll(c),s.test(e)&&i.test(e)?f[0].parentNode.childNodes:f;default:return d?d(e):(p=a(e,r).querySelector(r)).childNodes}}},5110:(e,t,n)=>{var r=n(6177),o=n(6270).formatDOM,i=/<(![a-zA-Z\s]+)>/;e.exports=function(e){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(""===e)return[];var t,n=e.match(i);return n&&n[1]&&(t=n[1]),o(r(e),null,t)}},6270:(e,t,n)=>{for(var r,o=n(5946),i=n(9003).CASE_SENSITIVE_TAG_NAMES,s=o.Comment,a=o.Element,l=o.ProcessingInstruction,c=o.Text,u={},p=0,d=i.length;p<d;p++)r=i[p],u[r.toLowerCase()]=r;function f(e){for(var t,n={},r=0,o=e.length;r<o;r++)n[(t=e[r]).name]=t.value;return n}function m(e){return function(e){return u[e]}(e=e.toLowerCase())||e}t.formatAttributes=f,t.formatDOM=function e(t,n,r){n=n||null;for(var o=[],i=0,u=t.length;i<u;i++){var p,d=t[i];switch(d.nodeType){case 1:(p=new a(m(d.nodeName),f(d.attributes))).children=e(d.childNodes,p);break;case 3:p=new c(d.nodeValue);break;case 8:p=new s(d.nodeValue);break;default:continue}var h=o[i-1]||null;h&&(h.next=p),p.parent=n,p.prev=h,p.next=null,o.push(p)}return r&&((p=new l(r.substring(0,r.indexOf(" ")).toLowerCase(),r)).next=o[0]||null,p.parent=n,o.unshift(p),o[1]&&(o[1].prev=o[0])),o}},2844:(e,t,n)=>{var r=n(5642),o=n(6414),i=n(5110);i="function"==typeof i.default?i.default:i;var s={lowerCaseAttributeNames:!1};function a(e,t){if("string"!=typeof e)throw new TypeError("First argument must be a string");return""===e?[]:r(i(e,(t=t||{}).htmlparser2||s),t)}a.domToReact=r,a.htmlToDOM=i,a.attributesToProps=o,a.Element=n(5946).Element,e.exports=a,e.exports.default=a},6414:(e,t,n)=>{var r=n(2138),o=n(6076);function i(e){return r.possibleStandardNames[e]}e.exports=function(e){var t,n,s,a,l,c={},u=(e=e||{}).type&&{reset:!0,submit:!0}[e.type];for(t in e)if(s=e[t],r.isCustomAttribute(t))c[t]=s;else if(a=i(n=t.toLowerCase()))switch(l=r.getPropertyInfo(a),"checked"!==a&&"value"!==a||u||(a=i("default"+n)),c[a]=s,l&&l.type){case r.BOOLEAN:c[a]=!0;break;case r.OVERLOADED_BOOLEAN:""===s&&(c[a]=!0)}else o.PRESERVE_CUSTOM_ATTRIBUTES&&(c[t]=s);return o.setStyleProp(e.style,c),c}},5642:(e,t,n)=>{var r=n(1609),o=n(6414),i=n(6076),s=i.setStyleProp,a=i.canTextBeChildOfNode;function l(e){return i.PRESERVE_CUSTOM_ATTRIBUTES&&"tag"===e.type&&i.isCustomComponent(e.name,e.attribs)}e.exports=function e(t,n){for(var i,c,u,p,d,f=(n=n||{}).library||r,m=f.cloneElement,h=f.createElement,g=f.isValidElement,v=[],y="function"==typeof n.replace,b=n.trim,w=0,E=t.length;w<E;w++)if(i=t[w],y&&g(u=n.replace(i)))E>1&&(u=m(u,{key:u.key||w})),v.push(u);else if("text"!==i.type){switch(p=i.attribs,l(i)?s(p.style,p):p&&(p=o(p)),d=null,i.type){case"script":case"style":i.children[0]&&(p.dangerouslySetInnerHTML={__html:i.children[0].data});break;case"tag":"textarea"===i.name&&i.children[0]?p.defaultValue=i.children[0].data:i.children&&i.children.length&&(d=e(i.children,n));break;default:continue}E>1&&(p.key=w),v.push(h(i.name,p,d))}else{if((c=!i.data.trim().length)&&i.parent&&!a(i.parent))continue;if(b&&c)continue;v.push(i.data)}return 1===v.length?v[0]:v}},6076:(e,t,n)=>{var r=n(1609),o=n(2266).default,i={reactCompat:!0},s=r.version.split(".")[0]>=16,a=new Set(["tr","tbody","thead","tfoot","colgroup","table","head","html","frameset"]);e.exports={PRESERVE_CUSTOM_ATTRIBUTES:s,invertObject:function(e,t){if(!e||"object"!=typeof e)throw new TypeError("First argument must be an object");var n,r,o="function"==typeof t,i={},s={};for(n in e)r=e[n],o&&(i=t(n,r))&&2===i.length?s[i[0]]=i[1]:"string"==typeof r&&(s[r]=n);return s},isCustomComponent:function(e,t){if(-1===e.indexOf("-"))return t&&"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}},setStyleProp:function(e,t){if(null!=e)try{t.style=o(e,i)}catch(e){t.style={}}},canTextBeChildOfNode:function(e){return!a.has(e.name)},elementsWithNoTextChildren:a}},3178:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,o=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,s=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,l=/^\s+|\s+$/g,c="";function u(e){return e?e.replace(l,c):c}e.exports=function(e,l){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];l=l||{};var p=1,d=1;function f(e){var t=e.match(n);t&&(p+=t.length);var r=e.lastIndexOf("\n");d=~r?e.length-r:d+e.length}function m(){var e={line:p,column:d};return function(t){return t.position=new h(e),b(),t}}function h(e){this.start=e,this.end={line:p,column:d},this.source=l.source}h.prototype.content=e;var g=[];function v(t){var n=new Error(l.source+":"+p+":"+d+": "+t);if(n.reason=t,n.filename=l.source,n.line=p,n.column=d,n.source=e,!l.silent)throw n;g.push(n)}function y(t){var n=t.exec(e);if(n){var r=n[0];return f(r),e=e.slice(r.length),n}}function b(){y(r)}function w(e){var t;for(e=e||[];t=E();)!1!==t&&e.push(t);return e}function E(){var t=m();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;c!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,c===e.charAt(n-1))return v("End of comment missing");var r=e.slice(2,n-2);return d+=2,f(r),e=e.slice(n),d+=2,t({type:"comment",comment:r})}}function x(){var e=m(),n=y(o);if(n){if(E(),!y(i))return v("property missing ':'");var r=y(s),l=e({type:"declaration",property:u(n[0].replace(t,c)),value:r?u(r[0].replace(t,c)):c});return y(a),l}}return b(),function(){var e,t=[];for(w(t);e=x();)!1!==e&&(t.push(e),w(t));return t}()}},385:(e,t,n)=>{"use strict";function r(e){return e.toLowerCase()}n.d(t,{g:()=>r})},4738:(e,t,n)=>{"use strict";if(n.d(t,{W:()=>s}),/^(1|819)$/.test(n.j))var r=n(385);var o=/^(1|819)$/.test(n.j)?[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g]:null,i=/[^A-Z0-9]+/gi;function s(e,t){void 0===t&&(t={});for(var n=t.splitRegexp,s=void 0===n?o:n,l=t.stripRegexp,c=void 0===l?i:l,u=t.transform,p=void 0===u?r.g:u,d=t.delimiter,f=void 0===d?" ":d,m=a(a(e,s,"$1\0$2"),c,"\0"),h=0,g=m.length;"\0"===m.charAt(h);)h++;for(;"\0"===m.charAt(g-1);)g--;return m.slice(h,g).split("\0").map(p).join(f)}function a(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}},7356:(e,t,n)=>{"use strict";if(n.d(t,{c:()=>i}),/^(1|819)$/.test(n.j))var r=n(5029);if(/^(1|819)$/.test(n.j))var o=n(4420);function i(e,t){return void 0===t&&(t={}),(0,o.a)(e,(0,r.Cl)({delimiter:"-"},t))}},2138:(e,t,n)=>{"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t,n,r,o,i,s){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}Object.defineProperty(t,"__esModule",{value:!0});var i={};["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"].forEach((function(e){i[e]=new o(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t,n,s=(n=2,function(e){if(Array.isArray(e))return e}(t=e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i=[],_n=!0,s=!1;try{for(n=n.call(e);!(_n=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);_n=!0);}catch(e){s=!0,o=e}finally{try{_n||null==n.return||n.return()}finally{if(s)throw o}}return i}}(t,n)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=s[0],l=s[1];i[a]=new o(a,1,!1,l,null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){i[e]=new o(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){i[e]=new o(e,2,!1,e,null,!1,!1)})),["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","disableRemotePlayback","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach((function(e){i[e]=new o(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){i[e]=new o(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){i[e]=new o(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){i[e]=new o(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){i[e]=new o(e,5,!1,e.toLowerCase(),null,!1,!1)}));var s=/[\-\:]([a-z])/g,a=function(e){return e[1].toUpperCase()};["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach((function(e){var t=e.replace(s,a);i[t]=new o(t,1,!1,e,null,!1,!1)})),["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach((function(e){var t=e.replace(s,a);i[t]=new o(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(s,a);i[t]=new o(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){i[e]=new o(e,1,!1,e.toLowerCase(),null,!1,!1)})),i.xlinkHref=new o("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){i[e]=new o(e,1,!1,e.toLowerCase(),null,!0,!0)}));var l=n(3603),c=l.CAMELCASE,u=l.SAME,p=l.possibleStandardNames,d=RegExp.prototype.test.bind(new RegExp("^(data|aria)-[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$")),f=Object.keys(p).reduce((function(e,t){var n=p[t];return n===u?e[t]=t:n===c?e[t.toLowerCase()]=t:e[t]=n,e}),{});t.BOOLEAN=3,t.BOOLEANISH_STRING=2,t.NUMERIC=5,t.OVERLOADED_BOOLEAN=4,t.POSITIVE_NUMERIC=6,t.RESERVED=0,t.STRING=1,t.getPropertyInfo=function(e){return i.hasOwnProperty(e)?i[e]:null},t.isCustomAttribute=d,t.possibleStandardNames=f},3603:(e,t)=>{t.SAME=0,t.CAMELCASE=1,t.possibleStandardNames={accept:0,acceptCharset:1,"accept-charset":"acceptCharset",accessKey:1,action:0,allowFullScreen:1,alt:0,as:0,async:0,autoCapitalize:1,autoComplete:1,autoCorrect:1,autoFocus:1,autoPlay:1,autoSave:1,capture:0,cellPadding:1,cellSpacing:1,challenge:0,charSet:1,checked:0,children:0,cite:0,class:"className",classID:1,className:1,cols:0,colSpan:1,content:0,contentEditable:1,contextMenu:1,controls:0,controlsList:1,coords:0,crossOrigin:1,dangerouslySetInnerHTML:1,data:0,dateTime:1,default:0,defaultChecked:1,defaultValue:1,defer:0,dir:0,disabled:0,disablePictureInPicture:1,disableRemotePlayback:1,download:0,draggable:0,encType:1,enterKeyHint:1,for:"htmlFor",form:0,formMethod:1,formAction:1,formEncType:1,formNoValidate:1,formTarget:1,frameBorder:1,headers:0,height:0,hidden:0,high:0,href:0,hrefLang:1,htmlFor:1,httpEquiv:1,"http-equiv":"httpEquiv",icon:0,id:0,innerHTML:1,inputMode:1,integrity:0,is:0,itemID:1,itemProp:1,itemRef:1,itemScope:1,itemType:1,keyParams:1,keyType:1,kind:0,label:0,lang:0,list:0,loop:0,low:0,manifest:0,marginWidth:1,marginHeight:1,max:0,maxLength:1,media:0,mediaGroup:1,method:0,min:0,minLength:1,multiple:0,muted:0,name:0,noModule:1,nonce:0,noValidate:1,open:0,optimum:0,pattern:0,placeholder:0,playsInline:1,poster:0,preload:0,profile:0,radioGroup:1,readOnly:1,referrerPolicy:1,rel:0,required:0,reversed:0,role:0,rows:0,rowSpan:1,sandbox:0,scope:0,scoped:0,scrolling:0,seamless:0,selected:0,shape:0,size:0,sizes:0,span:0,spellCheck:1,src:0,srcDoc:1,srcLang:1,srcSet:1,start:0,step:0,style:0,summary:0,tabIndex:1,target:0,title:0,type:0,useMap:1,value:0,width:0,wmode:0,wrap:0,about:0,accentHeight:1,"accent-height":"accentHeight",accumulate:0,additive:0,alignmentBaseline:1,"alignment-baseline":"alignmentBaseline",allowReorder:1,alphabetic:0,amplitude:0,arabicForm:1,"arabic-form":"arabicForm",ascent:0,attributeName:1,attributeType:1,autoReverse:1,azimuth:0,baseFrequency:1,baselineShift:1,"baseline-shift":"baselineShift",baseProfile:1,bbox:0,begin:0,bias:0,by:0,calcMode:1,capHeight:1,"cap-height":"capHeight",clip:0,clipPath:1,"clip-path":"clipPath",clipPathUnits:1,clipRule:1,"clip-rule":"clipRule",color:0,colorInterpolation:1,"color-interpolation":"colorInterpolation",colorInterpolationFilters:1,"color-interpolation-filters":"colorInterpolationFilters",colorProfile:1,"color-profile":"colorProfile",colorRendering:1,"color-rendering":"colorRendering",contentScriptType:1,contentStyleType:1,cursor:0,cx:0,cy:0,d:0,datatype:0,decelerate:0,descent:0,diffuseConstant:1,direction:0,display:0,divisor:0,dominantBaseline:1,"dominant-baseline":"dominantBaseline",dur:0,dx:0,dy:0,edgeMode:1,elevation:0,enableBackground:1,"enable-background":"enableBackground",end:0,exponent:0,externalResourcesRequired:1,fill:0,fillOpacity:1,"fill-opacity":"fillOpacity",fillRule:1,"fill-rule":"fillRule",filter:0,filterRes:1,filterUnits:1,floodOpacity:1,"flood-opacity":"floodOpacity",floodColor:1,"flood-color":"floodColor",focusable:0,fontFamily:1,"font-family":"fontFamily",fontSize:1,"font-size":"fontSize",fontSizeAdjust:1,"font-size-adjust":"fontSizeAdjust",fontStretch:1,"font-stretch":"fontStretch",fontStyle:1,"font-style":"fontStyle",fontVariant:1,"font-variant":"fontVariant",fontWeight:1,"font-weight":"fontWeight",format:0,from:0,fx:0,fy:0,g1:0,g2:0,glyphName:1,"glyph-name":"glyphName",glyphOrientationHorizontal:1,"glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphOrientationVertical:1,"glyph-orientation-vertical":"glyphOrientationVertical",glyphRef:1,gradientTransform:1,gradientUnits:1,hanging:0,horizAdvX:1,"horiz-adv-x":"horizAdvX",horizOriginX:1,"horiz-origin-x":"horizOriginX",ideographic:0,imageRendering:1,"image-rendering":"imageRendering",in2:0,in:0,inlist:0,intercept:0,k1:0,k2:0,k3:0,k4:0,k:0,kernelMatrix:1,kernelUnitLength:1,kerning:0,keyPoints:1,keySplines:1,keyTimes:1,lengthAdjust:1,letterSpacing:1,"letter-spacing":"letterSpacing",lightingColor:1,"lighting-color":"lightingColor",limitingConeAngle:1,local:0,markerEnd:1,"marker-end":"markerEnd",markerHeight:1,markerMid:1,"marker-mid":"markerMid",markerStart:1,"marker-start":"markerStart",markerUnits:1,markerWidth:1,mask:0,maskContentUnits:1,maskUnits:1,mathematical:0,mode:0,numOctaves:1,offset:0,opacity:0,operator:0,order:0,orient:0,orientation:0,origin:0,overflow:0,overlinePosition:1,"overline-position":"overlinePosition",overlineThickness:1,"overline-thickness":"overlineThickness",paintOrder:1,"paint-order":"paintOrder",panose1:0,"panose-1":"panose1",pathLength:1,patternContentUnits:1,patternTransform:1,patternUnits:1,pointerEvents:1,"pointer-events":"pointerEvents",points:0,pointsAtX:1,pointsAtY:1,pointsAtZ:1,prefix:0,preserveAlpha:1,preserveAspectRatio:1,primitiveUnits:1,property:0,r:0,radius:0,refX:1,refY:1,renderingIntent:1,"rendering-intent":"renderingIntent",repeatCount:1,repeatDur:1,requiredExtensions:1,requiredFeatures:1,resource:0,restart:0,result:0,results:0,rotate:0,rx:0,ry:0,scale:0,security:0,seed:0,shapeRendering:1,"shape-rendering":"shapeRendering",slope:0,spacing:0,specularConstant:1,specularExponent:1,speed:0,spreadMethod:1,startOffset:1,stdDeviation:1,stemh:0,stemv:0,stitchTiles:1,stopColor:1,"stop-color":"stopColor",stopOpacity:1,"stop-opacity":"stopOpacity",strikethroughPosition:1,"strikethrough-position":"strikethroughPosition",strikethroughThickness:1,"strikethrough-thickness":"strikethroughThickness",string:0,stroke:0,strokeDasharray:1,"stroke-dasharray":"strokeDasharray",strokeDashoffset:1,"stroke-dashoffset":"strokeDashoffset",strokeLinecap:1,"stroke-linecap":"strokeLinecap",strokeLinejoin:1,"stroke-linejoin":"strokeLinejoin",strokeMiterlimit:1,"stroke-miterlimit":"strokeMiterlimit",strokeWidth:1,"stroke-width":"strokeWidth",strokeOpacity:1,"stroke-opacity":"strokeOpacity",suppressContentEditableWarning:1,suppressHydrationWarning:1,surfaceScale:1,systemLanguage:1,tableValues:1,targetX:1,targetY:1,textAnchor:1,"text-anchor":"textAnchor",textDecoration:1,"text-decoration":"textDecoration",textLength:1,textRendering:1,"text-rendering":"textRendering",to:0,transform:0,typeof:0,u1:0,u2:0,underlinePosition:1,"underline-position":"underlinePosition",underlineThickness:1,"underline-thickness":"underlineThickness",unicode:0,unicodeBidi:1,"unicode-bidi":"unicodeBidi",unicodeRange:1,"unicode-range":"unicodeRange",unitsPerEm:1,"units-per-em":"unitsPerEm",unselectable:0,vAlphabetic:1,"v-alphabetic":"vAlphabetic",values:0,vectorEffect:1,"vector-effect":"vectorEffect",version:0,vertAdvY:1,"vert-adv-y":"vertAdvY",vertOriginX:1,"vert-origin-x":"vertOriginX",vertOriginY:1,"vert-origin-y":"vertOriginY",vHanging:1,"v-hanging":"vHanging",vIdeographic:1,"v-ideographic":"vIdeographic",viewBox:1,viewTarget:1,visibility:0,vMathematical:1,"v-mathematical":"vMathematical",vocab:0,widths:0,wordSpacing:1,"word-spacing":"wordSpacing",writingMode:1,"writing-mode":"writingMode",x1:0,x2:0,x:0,xChannelSelector:1,xHeight:1,"x-height":"xHeight",xlinkActuate:1,"xlink:actuate":"xlinkActuate",xlinkArcrole:1,"xlink:arcrole":"xlinkArcrole",xlinkHref:1,"xlink:href":"xlinkHref",xlinkRole:1,"xlink:role":"xlinkRole",xlinkShow:1,"xlink:show":"xlinkShow",xlinkTitle:1,"xlink:title":"xlinkTitle",xlinkType:1,"xlink:type":"xlinkType",xmlBase:1,"xml:base":"xmlBase",xmlLang:1,"xml:lang":"xmlLang",xmlns:0,"xml:space":"xmlSpace",xmlnsXlink:1,"xmlns:xlink":"xmlnsXlink",xmlSpace:1,y1:0,y2:0,y:0,yChannelSelector:1,z:0,zoomAndPan:1}},2266:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};t.__esModule=!0;var o=r(n(3637)),i=n(3454);t.default=function(e,t){var n={};return e&&"string"==typeof e?((0,o.default)(e,(function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)})),n):n}},3454:(e,t)=>{"use strict";t.__esModule=!0,t.camelCase=void 0;var n=/^--[a-zA-Z0-9-]+$/,r=/-([a-z])/g,o=/^[^-]+$/,i=/^-(webkit|moz|ms|o|khtml)-/,s=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},l=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){return void 0===t&&(t={}),function(e){return!e||o.test(e)||n.test(e)}(e)?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(s,l):e.replace(i,l)).replace(r,a))}},3637:(e,t,n)=>{var r=n(3178);e.exports=function(e,t){var n,o=null;if(!e||"string"!=typeof e)return o;for(var i,s,a=r(e),l="function"==typeof t,c=0,u=a.length;c<u;c++)i=(n=a[c]).property,s=n.value,l?t(i,s,n):s&&(o||(o={}),o[i]=s);return o}},4347:(e,t,n)=>{"use strict";n.d(t,{YQ:()=>o,d7:()=>a});var r=n(1609);function o(e,t,n){var o=this,i=(0,r.useRef)(null),s=(0,r.useRef)(0),a=(0,r.useRef)(null),l=(0,r.useRef)([]),c=(0,r.useRef)(),u=(0,r.useRef)(),p=(0,r.useRef)(e),d=(0,r.useRef)(!0);(0,r.useEffect)((function(){p.current=e}),[e]);var f=!t&&0!==t&&"undefined"!=typeof window;if("function"!=typeof e)throw new TypeError("Expected a function");t=+t||0;var m=!!(n=n||{}).leading,h=!("trailing"in n)||!!n.trailing,g="maxWait"in n,v=g?Math.max(+n.maxWait||0,t):null;(0,r.useEffect)((function(){return d.current=!0,function(){d.current=!1}}),[]);var y=(0,r.useMemo)((function(){var e=function(e){var t=l.current,n=c.current;return l.current=c.current=null,s.current=e,u.current=p.current.apply(n,t)},n=function(e,t){f&&cancelAnimationFrame(a.current),a.current=f?requestAnimationFrame(e):setTimeout(e,t)},r=function(e){if(!d.current)return!1;var n=e-i.current;return!i.current||n>=t||n<0||g&&e-s.current>=v},y=function(t){return a.current=null,h&&l.current?e(t):(l.current=c.current=null,u.current)},b=function e(){var o=Date.now();if(r(o))return y(o);if(d.current){var a=t-(o-i.current),l=g?Math.min(a,v-(o-s.current)):a;n(e,l)}},w=function(){var p=Date.now(),f=r(p);if(l.current=[].slice.call(arguments),c.current=o,i.current=p,f){if(!a.current&&d.current)return s.current=i.current,n(b,t),m?e(i.current):u.current;if(g)return n(b,t),e(i.current)}return a.current||n(b,t),u.current};return w.cancel=function(){a.current&&(f?cancelAnimationFrame(a.current):clearTimeout(a.current)),s.current=0,l.current=i.current=c.current=a.current=null},w.isPending=function(){return!!a.current},w.flush=function(){return a.current?y(Date.now()):u.current},w}),[m,g,t,v,h,f]);return y}function i(e,t){return e===t}function s(e){return"function"==typeof e?function(){return e}:e}function a(e,t,n){var a,l,c=n&&n.equalityFn||i,u=(a=(0,r.useState)(s(e)),l=a[1],[a[0],(0,r.useCallback)((function(e){return l(s(e))}),[])]),p=u[0],d=u[1],f=o((0,r.useCallback)((function(e){return d(e)}),[d]),t,n),m=(0,r.useRef)(e);return c(m.current,e)||(f(e),m.current=e),[p,f]}},6985:(e,t,n)=>{"use strict";function r(e,t){return"function"==typeof Object.hasOwn?Object.hasOwn(e,t):Object.prototype.hasOwnProperty.call(e,t)}function o(e){return e.disabled||!0===e["aria-disabled"]||"true"===e["aria-disabled"]}function i(e){const t={};for(const n in e)void 0!==e[n]&&(t[n]=e[n]);return t}n.d(t,{$f:()=>o,HR:()=>i,mQ:()=>r})},754:(e,t,n)=>{"use strict";n.d(t,{Bm:()=>l,Sw:()=>o,bq:()=>s,cK:()=>u,gR:()=>a,kp:()=>d,mB:()=>f,zN:()=>p});var r,o="undefined"!=typeof window&&!!(null==(r=window.document)?void 0:r.createElement);function i(e){return e?e.ownerDocument||e:document}function s(e,t=!1){const{activeElement:n}=i(e);if(!(null==n?void 0:n.nodeName))return null;if("IFRAME"===n.tagName&&n.contentDocument)return s(n.contentDocument.body,t);if(t){const e=n.getAttribute("aria-activedescendant");if(e){const t=i(n).getElementById(e);if(t)return t}}return n}function a(e,t){return e===t||e.contains(t)}function l(e){const t=e.tagName.toLowerCase();return"button"===t||!("input"!==t||!e.type)&&-1!==c.indexOf(e.type)}var c=932==n.j?["button","color","file","image","reset","submit"]:null;function u(e,t){return"matches"in e?e.matches(t):"msMatchesSelector"in e?e.msMatchesSelector(t):e.webkitMatchesSelector(t)}function p(e){const t=e;return t.offsetWidth>0||t.offsetHeight>0||e.getClientRects().length>0}function d(e,t){if("closest"in e)return e.closest(t);do{if(u(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}function f(e){try{const t=e instanceof HTMLInputElement&&null!==e.selectionStart,n="TEXTAREA"===e.tagName;return t||n||!1}catch(e){return!1}}},4823:(e,t,n)=>{"use strict";n.d(t,{IA:()=>u,YG:()=>d,ko:()=>p});var r=Object.defineProperty,o=Object.defineProperties,i=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,c=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,u=(e,t)=>{for(var n in t||(t={}))a.call(t,n)&&c(e,n,t[n]);if(s)for(var n of s(t))l.call(t,n)&&c(e,n,t[n]);return e},p=(e,t)=>o(e,i(t)),d=(e,t)=>{var n={};for(var r in e)a.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&s)for(var r of s(e))t.indexOf(r)<0&&l.call(e,r)&&(n[r]=e[r]);return n}},111:(e,t,n)=>{"use strict";n.d(t,{$:()=>D});var r=n(1609),o=(0,r.createContext)(!0),i=n(611),s=n(4951),a=n(4823),l=n(790);function c(e){const t=r.forwardRef(((t,n)=>e((0,a.ko)((0,a.IA)({},t),{ref:n}))));return t.displayName=e.displayName||e.name,t}function u(e,t){const n=t,{wrapElement:o,render:c}=n,u=(0,a.YG)(n,["wrapElement","render"]),p=(0,i.SV)(t.ref,(0,s.v1)(c));let d;if(r.isValidElement(c)){const e=(0,a.ko)((0,a.IA)({},c.props),{ref:p});d=r.cloneElement(c,(0,s.v6)(u,e))}else d=c?c(u):(0,l.jsx)(e,(0,a.IA)({},u));return o?o(d):d}function p(e){const t=(t={})=>e(t);return t.displayName=e.name,t}var d=n(754);function f(e){return e.target===e.currentTarget}function m(e,t){const n=new MouseEvent("click",t);return e.dispatchEvent(n)}function h(e,t,n){const r=requestAnimationFrame((()=>{e.removeEventListener(t,o,!0),n()})),o=()=>{cancelAnimationFrame(r),n()};return e.addEventListener(t,o,{once:!0,capture:!0}),r}function g(e,t,n,r=window){const o=[];try{r.document.addEventListener(e,t,n);for(const i of Array.from(r.frames))o.push(g(e,t,n,i))}catch(e){}return()=>{try{r.document.removeEventListener(e,t,n)}catch(e){}o.forEach((e=>e()))}}function v(e){return!!(0,d.cK)(e,"input:not([type='hidden']):not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], button:not([disabled]), [tabindex], summary, iframe, object, embed, area[href], audio[controls], video[controls], [contenteditable]:not([contenteditable='false'])")&&!!(0,d.zN)(e)&&!(0,d.kp)(e,"[inert]")}function y(e){const t=(0,d.bq)(e);if(!t)return!1;if(t===e)return!0;const n=t.getAttribute("aria-activedescendant");return!!n&&n===e.id}var b=n(6985),w=d.Sw&&!!d.Sw&&/mac|iphone|ipad|ipod/i.test(navigator.platform)&&/apple/i.test(navigator.vendor),E=["text","search","url","tel","email","password","number","date","month","week","time","datetime","datetime-local"];function x(e){return!("input"!==e.tagName.toLowerCase()||!e.type||"radio"!==e.type&&"checkbox"!==e.type)}function k(e,t,n,r,o){return e?t?n&&!r?-1:void 0:n?o:o||0:o}function T(e,t){return(0,i._q)((n=>{null==e||e(n),n.defaultPrevented||t&&(n.stopPropagation(),n.preventDefault())}))}var S=!0;function A(e){const t=e.target;t&&"hasAttribute"in t&&(t.hasAttribute("data-focus-visible")||(S=!1))}function C(e){e.metaKey||e.ctrlKey||e.altKey||(S=!0)}var O=p((function(e){var t=e,{focusable:n=!0,accessibleWhenDisabled:s,autoFocus:l,onFocusVisible:c}=t,u=(0,a.YG)(t,["focusable","accessibleWhenDisabled","autoFocus","onFocusVisible"]);const p=(0,r.useRef)(null);(0,r.useEffect)((()=>{n&&(g("mousedown",A,!0),g("keydown",C,!0))}),[n]),w&&(0,r.useEffect)((()=>{if(!n)return;const e=p.current;if(!e)return;if(!x(e))return;const t=function(e){return"labels"in e?e.labels:null}(e);if(!t)return;const r=()=>queueMicrotask((()=>e.focus()));return t.forEach((e=>e.addEventListener("mouseup",r))),()=>{t.forEach((e=>e.removeEventListener("mouseup",r)))}}),[n]);const m=n&&(0,b.$f)(u),O=!!m&&!s,[N,I]=(0,r.useState)(!1);(0,r.useEffect)((()=>{n&&O&&N&&I(!1)}),[n,O,N]),(0,r.useEffect)((()=>{if(!n)return;if(!N)return;const e=p.current;if(!e)return;if("undefined"==typeof IntersectionObserver)return;const t=new IntersectionObserver((()=>{v(e)||I(!1)}));return t.observe(e),()=>t.disconnect()}),[n,N]);const _=T(u.onKeyPressCapture,m),L=T(u.onMouseDownCapture,m),M=T(u.onClickCapture,m),D=u.onMouseDown,R=(0,i._q)((e=>{if(null==D||D(e),e.defaultPrevented)return;if(!n)return;const t=e.currentTarget;if(!w)return;if(function(e){return Boolean(e.currentTarget&&!(0,d.gR)(e.currentTarget,e.target))}(e))return;if(!(0,d.Bm)(t)&&!x(t))return;let r=!1;const o=()=>{r=!0};t.addEventListener("focusin",o,{capture:!0,once:!0}),h(t,"mouseup",(()=>{t.removeEventListener("focusin",o,!0),r||function(e){!function(e){const t=(0,d.bq)(e);if(!t)return!1;if((0,d.gR)(e,t))return!0;const n=t.getAttribute("aria-activedescendant");return!!n&&"id"in e&&(n===e.id||!!e.querySelector(`#${CSS.escape(n)}`))}(e)&&v(e)&&e.focus()}(t)}))})),P=(e,t)=>{if(t&&(e.currentTarget=t),!n)return;const r=e.currentTarget;r&&y(r)&&(null==c||c(e),e.defaultPrevented||I(!0))},F=u.onKeyDownCapture,j=(0,i._q)((e=>{if(null==F||F(e),e.defaultPrevented)return;if(!n)return;if(N)return;if(e.metaKey)return;if(e.altKey)return;if(e.ctrlKey)return;if(!f(e))return;const t=e.currentTarget;queueMicrotask((()=>P(e,t)))})),B=u.onFocusCapture,H=(0,i._q)((e=>{if(null==B||B(e),e.defaultPrevented)return;if(!n)return;if(!f(e))return void I(!1);const t=e.currentTarget,r=()=>P(e,t);S||function(e){const{tagName:t,readOnly:n,type:r}=e;return"TEXTAREA"===t&&!n||"SELECT"===t&&!n||("INPUT"!==t||n?!!e.isContentEditable:E.includes(r))}(e.target)?queueMicrotask(r):function(e){return"combobox"===e.getAttribute("role")&&!!e.dataset.name}(e.target)?h(e.target,"focusout",r):I(!1)})),z=u.onBlur,V=(0,i._q)((e=>{null==z||z(e),n&&function(e){const t=e.currentTarget,n=e.relatedTarget;return!n||!(0,d.gR)(t,n)}(e)&&I(!1)})),W=(0,r.useContext)(o),U=(0,i._q)((e=>{n&&l&&e&&W&&queueMicrotask((()=>{y(e)||v(e)&&e.focus()}))})),$=(0,i.vO)(p),q=n&&function(e){return!e||"button"===e||"summary"===e||"input"===e||"select"===e||"textarea"===e||"a"===e}($),K=n&&function(e){return!e||"button"===e||"input"===e||"select"===e||"textarea"===e}($),G=u.style,Y=(0,r.useMemo)((()=>O?(0,a.IA)({pointerEvents:"none"},G):G),[O,G]);return u=(0,a.ko)((0,a.IA)({"data-focus-visible":n&&N||void 0,"data-autofocus":l||void 0,"aria-disabled":m||void 0},u),{ref:(0,i.SV)(p,U,u.ref),style:Y,tabIndex:k(n,O,q,K,u.tabIndex),disabled:!(!K||!O)||void 0,contentEditable:m?void 0:u.contentEditable,onKeyPressCapture:_,onClickCapture:M,onMouseDownCapture:L,onMouseDown:R,onKeyDownCapture:j,onFocusCapture:H,onBlur:V}),(0,b.HR)(u)}));function N(e){if(!e.isTrusted)return!1;const t=e.currentTarget;return"Enter"===e.key?(0,d.Bm)(t)||"SUMMARY"===t.tagName||"A"===t.tagName:" "===e.key&&((0,d.Bm)(t)||"SUMMARY"===t.tagName||"INPUT"===t.tagName||"SELECT"===t.tagName)}c((function(e){return u("div",O(e))}));var I=Symbol("command"),_=p((function(e){var t=e,{clickOnEnter:n=!0,clickOnSpace:o=!0}=t,s=(0,a.YG)(t,["clickOnEnter","clickOnSpace"]);const l=(0,r.useRef)(null),c=(0,i.vO)(l),u=s.type,[p,g]=(0,r.useState)((()=>!!c&&(0,d.Bm)({tagName:c,type:u})));(0,r.useEffect)((()=>{l.current&&g((0,d.Bm)(l.current))}),[]);const[v,y]=(0,r.useState)(!1),w=(0,r.useRef)(!1),E=(0,b.$f)(s),[x,k]=(0,i.P1)(s,I,!0),T=s.onKeyDown,S=(0,i._q)((e=>{null==T||T(e);const t=e.currentTarget;if(e.defaultPrevented)return;if(x)return;if(E)return;if(!f(e))return;if((0,d.mB)(t))return;if(t.isContentEditable)return;const r=n&&"Enter"===e.key,i=o&&" "===e.key,s="Enter"===e.key&&!n,l=" "===e.key&&!o;if(s||l)e.preventDefault();else if(r||i){const n=N(e);if(r){if(!n){e.preventDefault();const n=e,{view:r}=n,o=(0,a.YG)(n,["view"]),i=()=>m(t,o);d.Sw&&/firefox\//i.test(navigator.userAgent)?h(t,"keyup",i):queueMicrotask(i)}}else i&&(w.current=!0,n||(e.preventDefault(),y(!0)))}})),A=s.onKeyUp,C=(0,i._q)((e=>{if(null==A||A(e),e.defaultPrevented)return;if(x)return;if(E)return;if(e.metaKey)return;const t=o&&" "===e.key;if(w.current&&t&&(w.current=!1,!N(e))){e.preventDefault(),y(!1);const t=e.currentTarget,n=e,{view:r}=n,o=(0,a.YG)(n,["view"]);queueMicrotask((()=>m(t,o)))}}));return s=(0,a.ko)((0,a.IA)((0,a.IA)({"data-active":v||void 0,type:p?"button":void 0},k),s),{ref:(0,i.SV)(l,s.ref),onKeyDown:S,onKeyUp:C}),O(s)})),L=(c((function(e){return u("button",_(e))})),"button"),M=p((function(e){const t=(0,r.useRef)(null),n=(0,i.vO)(t,L),[o,s]=(0,r.useState)((()=>!!n&&(0,d.Bm)({tagName:n,type:e.type})));return(0,r.useEffect)((()=>{t.current&&s((0,d.Bm)(t.current))}),[]),e=(0,a.ko)((0,a.IA)({role:o||"a"===n?void 0:"button"},e),{ref:(0,i.SV)(t,e.ref)}),e=_(e)})),D=c((function(e){const t=M(e);return u(L,t)}))},611:(e,t,n)=>{"use strict";var r;if(n.d(t,{P1:()=>m,SV:()=>d,_q:()=>p,vO:()=>f}),932==n.j)var o=n(4951);var i=n(4823),s=n(1609);if(932==n.j)var a=n(754);var l=(0,i.IA)({},r||(r=n.t(s,2))),c=(l.useId,l.useDeferredValue,l.useInsertionEffect),u=932==n.j?a.Sw?s.useLayoutEffect:s.useEffect:null;function p(e){const t=(0,s.useRef)((()=>{throw new Error("Cannot call an event handler while rendering.")}));return c?c((()=>{t.current=e})):t.current=e,(0,s.useCallback)(((...e)=>{var n;return null==(n=t.current)?void 0:n.call(t,...e)}),[])}function d(...e){return(0,s.useMemo)((()=>{if(e.some(Boolean))return t=>{e.forEach((e=>(0,o.cZ)(e,t)))}}),e)}function f(e,t){const n=e=>{if("string"==typeof e)return e},[r,o]=(0,s.useState)((()=>n(t)));return u((()=>{const r=e&&"current"in e?e.current:e;o((null==r?void 0:r.tagName.toLowerCase())||n(t))}),[e,t]),r}function m(e,t,n){const r=e.onLoadedMetadataCapture,o=(0,s.useMemo)((()=>Object.assign((()=>{}),(0,i.ko)((0,i.IA)({},r),{[t]:n}))),[r,t,n]);return[null==r?void 0:r[t],{onLoadedMetadataCapture:o}]}Symbol("setNextState")},4951:(e,t,n)=>{"use strict";if(n.d(t,{cZ:()=>s,v1:()=>a,v6:()=>l}),932==n.j)var r=n(4823);var o=n(1609);if(932==n.j)var i=n(6985);function s(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function a(e){return function(e){return!!e&&!!(0,o.isValidElement)(e)&&"ref"in e}(e)?e.ref:null}function l(e,t){const n=(0,r.IA)({},e);for(const o in t){if(!(0,i.mQ)(t,o))continue;if("className"===o){const r="className";n[r]=e[r]?`${e[r]} ${t[r]}`:t[r];continue}if("style"===o){const o="style";n[o]=e[o]?(0,r.IA)((0,r.IA)({},e[o]),t[o]):t[o];continue}const s=t[o];if("function"==typeof s&&o.startsWith("on")){const t=e[o];if("function"==typeof t){n[o]=(...e)=>{s(...e),t(...e)};continue}}n[o]=s}return n}},2509:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}n.d(t,{A:()=>r})},3558:(e,t,n)=>{"use strict";n.d(t,{Bi:()=>s});var r={exports:{}};const o=[{id:0,value:"Too weak",minDiversity:0,minLength:0},{id:1,value:"Weak",minDiversity:2,minLength:6},{id:2,value:"Medium",minDiversity:4,minLength:8},{id:3,value:"Strong",minDiversity:4,minLength:10}],i=(e,t=o,n="!\"#$%&'()*+,-./:;<=>?@[\\\\\\]^_`{|}~")=>{let r=e||"";t[0].minDiversity=0,t[0].minLength=0;const i=[{regex:"[a-z]",message:"lowercase"},{regex:"[A-Z]",message:"uppercase"},{regex:"[0-9]",message:"number"}];n&&i.push({regex:`[${n}]`,message:"symbol"});let s={};s.contains=i.filter((e=>new RegExp(`${e.regex}`).test(r))).map((e=>e.message)),s.length=r.length;let a=t.filter((e=>s.contains.length>=e.minDiversity)).filter((e=>s.length>=e.minLength)).sort(((e,t)=>t.id-e.id)).map((e=>({id:e.id,value:e.value})));return Object.assign(s,a[0]),s};r.exports={passwordStrength:i,defaultOptions:o};var s=r.exports.passwordStrength=i;r.exports.defaultOptions=o,r.exports},4921:(e,t,n)=>{"use strict";function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}n.d(t,{A:()=>o});const o=function(){for(var e,t,n=0,o="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o}},9456:(e,t,n)=>{"use strict";n.d(t,{di:()=>u});var r=function(e){return function(t,n,r){return e(t,n,r)*r}},o=function(e,t){if(e)throw Error("Invalid sort config: "+t)},i=function(e){var t=e||{},n=t.asc,i=t.desc,s=n?1:-1,a=n||i;return o(!a,"Expected `asc` or `desc` property"),o(n&&i,"Ambiguous object with `asc` and `desc` config properties"),{order:s,sortBy:a,comparer:e.comparer&&r(e.comparer)}};function s(e,t,n){if(void 0===e||!0===e)return function(e,r){return t(e,r,n)};if("string"==typeof e)return o(e.includes("."),"String syntax not allowed for nested properties."),function(r,o){return t(r[e],o[e],n)};if("function"==typeof e)return function(r,o){return t(e(r),e(o),n)};if(Array.isArray(e)){var r=function(e){return function t(n,r,o,s,a,l,c){var u,p;if("string"==typeof n)u=l[n],p=c[n];else{if("function"!=typeof n){var d=i(n);return t(d.sortBy,r,o,d.order,d.comparer||e,l,c)}u=n(l),p=n(c)}var f=a(u,p,s);return(0===f||null==u&&null==p)&&r.length>o?t(r[o],r,o+1,s,a,l,c):f}}(t);return function(o,i){return r(e[0],e,1,n,t,o,i)}}var a=i(e);return s(a.sortBy,a.comparer||t,a.order)}var a=function(e,t,n,r){return Array.isArray(t)?(Array.isArray(n)&&n.length<2&&(n=n[0]),t.sort(s(n,r,e))):t};function l(e){var t=r(e.comparer);return function(n){var r=Array.isArray(n)&&!e.inPlaceSorting?n.slice():n;return{asc:function(e){return a(1,r,e,t)},desc:function(e){return a(-1,r,e,t)},by:function(e){return a(1,r,e,t)}}}}var c=function(e,t,n){return null==e?n:null==t?-n:typeof e!=typeof t?typeof e<typeof t?-1:1:e<t?-1:e>t?1:0},u=l({comparer:c});l({comparer:c,inPlaceSorting:!0})},8314:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>o});var r=n(2844);r.domToReact,r.htmlToDOM,r.attributesToProps,r.Element;const o=819==n.j?r:null},5029:(e,t,n)=>{"use strict";n.d(t,{Cl:()=>r});var r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)};Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError}}]);
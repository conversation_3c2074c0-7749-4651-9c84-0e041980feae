<?php
return [
0xA9 => 0x28,
0xAA => 0x61,
0xB0 => 0x30,
0xB2 => 0x32,
0xB3 => 0x33,
0xB5 => 0x75,
0xB9 => 0x31,
0xBA => 0x6F,
0xC0 => 0x41,
0xC1 => 0x41,
0xC2 => 0x41,
0xC3 => 0x41,
0xC4 => 0x41,
0xC5 => 0x41,
0xC6 => 0x41,
0xC7 => 0x43,
0xC8 => 0x45,
0xC9 => 0x45,
0xCA => 0x45,
0xCB => 0x45,
0xCC => 0x49,
0xCD => 0x49,
0xCE => 0x49,
0xCF => 0x49,
0xD0 => 0x44,
0xD1 => 0x4E,
0xD2 => 0x4F,
0xD3 => 0x4F,
0xD4 => 0x4F,
0xD5 => 0x4F,
0xD6 => 0x4F,
0xD8 => 0x4F,
0xD9 => 0x55,
0xDA => 0x55,
0xDB => 0x55,
0xDC => 0x55,
0xDD => 0x59,
0xDE => 0x54,
0xDF => 0x73,
0xE0 => 0x61,
0xE1 => 0x61,
0xE2 => 0x61,
0xE3 => 0x61,
0xE4 => 0x61,
0xE5 => 0x61,
0xE6 => 0x61,
0xE7 => 0x63,
0xE8 => 0x65,
0xE9 => 0x65,
0xEA => 0x65,
0xEB => 0x65,
0xEC => 0x69,
0xED => 0x69,
0xEE => 0x69,
0xEF => 0x69,
0xF0 => 0x64,
0xF1 => 0x6E,
0xF2 => 0x6F,
0xF3 => 0x6F,
0xF4 => 0x6F,
0xF5 => 0x6F,
0xF6 => 0x6F,
0xF8 => 0x6F,
0xF9 => 0x75,
0xFA => 0x75,
0xFB => 0x75,
0xFC => 0x75,
0xFD => 0x79,
0xFE => 0x74,
0xFF => 0x79,
0x100 => 0x41,
0x101 => 0x61,
0x102 => 0x41,
0x103 => 0x61,
0x104 => 0x41,
0x105 => 0x61,
0x106 => 0x43,
0x107 => 0x63,
0x108 => 0x43,
0x109 => 0x63,
0x10A => 0x43,
0x10B => 0x63,
0x10C => 0x43,
0x10D => 0x63,
0x10E => 0x44,
0x10F => 0x64,
0x110 => 0x44,
0x111 => 0x64,
0x112 => 0x45,
0x113 => 0x65,
0x114 => 0x45,
0x115 => 0x65,
0x116 => 0x45,
0x117 => 0x65,
0x118 => 0x45,
0x119 => 0x65,
0x11A => 0x45,
0x11B => 0x65,
0x11C => 0x47,
0x11D => 0x67,
0x11E => 0x47,
0x11F => 0x67,
0x120 => 0x47,
0x121 => 0x67,
0x122 => 0x47,
0x123 => 0x67,
0x124 => 0x48,
0x125 => 0x68,
0x126 => 0x48,
0x127 => 0x68,
0x128 => 0x49,
0x129 => 0x69,
0x12A => 0x49,
0x12B => 0x69,
0x12C => 0x49,
0x12D => 0x69,
0x12E => 0x49,
0x12F => 0x69,
0x130 => 0x49,
0x131 => 0x69,
0x132 => 0x49,
0x133 => 0x69,
0x134 => 0x4A,
0x135 => 0x6A,
0x136 => 0x6B,
0x137 => 0x6B,
0x138 => 0x6B,
0x139 => 0x4C,
0x13A => 0x6C,
0x13B => 0x4C,
0x13C => 0x6C,
0x13D => 0x4C,
0x13E => 0x6C,
0x13F => 0x4C,
0x140 => 0x6C,
0x141 => 0x4C,
0x142 => 0x6C,
0x143 => 0x4E,
0x144 => 0x6E,
0x145 => 0x4E,
0x146 => 0x6E,
0x147 => 0x4E,
0x148 => 0x6E,
0x149 => 0x6E,
0x14A => 0x4E,
0x14B => 0x6E,
0x14C => 0x4F,
0x14D => 0x6F,
0x14E => 0x4F,
0x14F => 0x6F,
0x150 => 0x4F,
0x151 => 0x6F,
0x152 => 0x4F,
0x153 => 0x6F,
0x154 => 0x52,
0x155 => 0x72,
0x156 => 0x52,
0x157 => 0x72,
0x158 => 0x52,
0x159 => 0x72,
0x15A => 0x53,
0x15B => 0x73,
0x15C => 0x53,
0x15D => 0x73,
0x15E => 0x53,
0x15F => 0x73,
0x160 => 0x53,
0x161 => 0x73,
0x162 => 0x54,
0x163 => 0x74,
0x164 => 0x54,
0x165 => 0x74,
0x166 => 0x54,
0x167 => 0x74,
0x168 => 0x55,
0x169 => 0x75,
0x16A => 0x55,
0x16B => 0x75,
0x16C => 0x55,
0x16D => 0x75,
0x16E => 0x55,
0x16F => 0x75,
0x170 => 0x55,
0x171 => 0x75,
0x172 => 0x55,
0x173 => 0x75,
0x174 => 0x57,
0x175 => 0x77,
0x176 => 0x59,
0x177 => 0x79,
0x178 => 0x59,
0x179 => 0x5A,
0x17A => 0x7A,
0x17B => 0x5A,
0x17C => 0x7A,
0x17D => 0x5A,
0x17E => 0x7A,
0x17F => 0x73,
0x189 => 0x44,
0x18A => 0x44,
0x18B => 0x44,
0x18C => 0x64,
0x18F => 0x45,
0x192 => 0x66,
0x1A0 => 0x4F,
0x1A1 => 0x6F,
0x1AF => 0x55,
0x1B0 => 0x75,
0x1CD => 0x41,
0x1CE => 0x61,
0x1CF => 0x49,
0x1D0 => 0x69,
0x1D1 => 0x4F,
0x1D2 => 0x6F,
0x1D3 => 0x55,
0x1D4 => 0x75,
0x1D5 => 0x55,
0x1D6 => 0x75,
0x1D7 => 0x55,
0x1D8 => 0x75,
0x1D9 => 0x55,
0x1DA => 0x75,
0x1DB => 0x55,
0x1DC => 0x75,
0x1FA => 0x41,
0x1FB => 0x61,
0x1FC => 0x41,
0x1FD => 0x61,
0x1FE => 0x4F,
0x1FF => 0x6F,
0x218 => 0x53,
0x219 => 0x73,
0x21A => 0x54,
0x21B => 0x74,
0x221 => 0x64,
0x256 => 0x64,
0x257 => 0x64,
0x259 => 0x65,
0x386 => 0x41,
0x388 => 0x45,
0x389 => 0x48,
0x38A => 0x49,
0x38C => 0x4F,
0x38E => 0x59,
0x38F => 0x57,
0x390 => 0x69,
0x391 => 0x41,
0x392 => 0x42,
0x393 => 0x47,
0x394 => 0x44,
0x395 => 0x45,
0x396 => 0x5A,
0x397 => 0x48,
0x398 => 0x4F,
0x399 => 0x49,
0x39A => 0x4B,
0x39B => 0x4C,
0x39C => 0x4D,
0x39D => 0x4E,
0x39E => 0x58,
0x39F => 0x4F,
0x3A0 => 0x50,
0x3A1 => 0x52,
0x3A3 => 0x53,
0x3A4 => 0x54,
0x3A5 => 0x59,
0x3A6 => 0x46,
0x3A7 => 0x58,
0x3A8 => 0x50,
0x3A9 => 0x57,
0x3AA => 0x49,
0x3AB => 0x59,
0x3AC => 0x61,
0x3AD => 0x65,
0x3AE => 0x68,
0x3AF => 0x69,
0x3B0 => 0x79,
0x3B1 => 0x61,
0x3B2 => 0x62,
0x3B3 => 0x67,
0x3B4 => 0x64,
0x3B5 => 0x65,
0x3B6 => 0x7A,
0x3B7 => 0x68,
0x3B8 => 0x6F,
0x3B9 => 0x69,
0x3BA => 0x6B,
0x3BB => 0x6C,
0x3BC => 0x6D,
0x3BD => 0x6E,
0x3BE => 0x78,
0x3BF => 0x6F,
0x3C0 => 0x70,
0x3C1 => 0x72,
0x3C2 => 0x73,
0x3C3 => 0x73,
0x3C4 => 0x74,
0x3C5 => 0x79,
0x3C6 => 0x66,
0x3C7 => 0x78,
0x3C8 => 0x70,
0x3C9 => 0x77,
0x3CA => 0x69,
0x3CB => 0x79,
0x3CC => 0x6F,
0x3CD => 0x79,
0x3CE => 0x77,
0x3D0 => 0x76,
0x3D1 => 0x74,
0x3D2 => 0x49,
0x401 => 0x45,
0x402 => 0x44,
0x404 => 0x45,
0x406 => 0x49,
0x407 => 0x49,
0x408 => 0x6A,
0x409 => 0x4C,
0x40A => 0x4E,
0x40F => 0x44,
0x410 => 0x41,
0x411 => 0x42,
0x412 => 0x56,
0x413 => 0x47,
0x414 => 0x44,
0x415 => 0x45,
0x416 => 0x5A,
0x417 => 0x5A,
0x418 => 0x49,
0x419 => 0x59,
0x41A => 0x4B,
0x41B => 0x4C,
0x41C => 0x4D,
0x41D => 0x4E,
0x41E => 0x4F,
0x41F => 0x50,
0x420 => 0x52,
0x421 => 0x53,
0x422 => 0x54,
0x423 => 0x55,
0x424 => 0x46,
0x425 => 0x4B,
0x426 => 0x54,
0x427 => 0x43,
0x428 => 0x53,
0x429 => 0x53,
0x42A => 0x62,
0x42B => 0x59,
0x42C => 0x62,
0x42D => 0x45,
0x42E => 0x59,
0x42F => 0x59,
0x430 => 0x61,
0x431 => 0x62,
0x432 => 0x76,
0x433 => 0x67,
0x434 => 0x64,
0x435 => 0x65,
0x436 => 0x7A,
0x437 => 0x7A,
0x438 => 0x69,
0x439 => 0x79,
0x43A => 0x6B,
0x43B => 0x6C,
0x43C => 0x6D,
0x43D => 0x6E,
0x43E => 0x6F,
0x43F => 0x70,
0x440 => 0x72,
0x441 => 0x73,
0x442 => 0x74,
0x443 => 0x75,
0x444 => 0x66,
0x445 => 0x6B,
0x446 => 0x74,
0x447 => 0x63,
0x448 => 0x73,
0x449 => 0x73,
0x44B => 0x79,
0x44D => 0x65,
0x44E => 0x79,
0x44F => 0x79,
0x451 => 0x65,
0x452 => 0x64,
0x454 => 0x65,
0x456 => 0x69,
0x457 => 0x69,
0x458 => 0x6A,
0x459 => 0x6C,
0x45A => 0x6E,
0x45F => 0x64,
0x490 => 0x47,
0x491 => 0x67,
0x4E8 => 0x4F,
0x622 => 0x61,
0x623 => 0x61,
0x624 => 0x6F,
0x625 => 0x65,
0x626 => 0x65,
0x627 => 0x61,
0x628 => 0x62,
0x62A => 0x74,
0x62B => 0x74,
0x62C => 0x6A,
0x62D => 0x68,
0x62E => 0x6B,
0x62F => 0x64,
0x630 => 0x74,
0x631 => 0x72,
0x632 => 0x7A,
0x633 => 0x73,
0x634 => 0x73,
0x635 => 0x73,
0x636 => 0x64,
0x637 => 0x74,
0x638 => 0x74,
0x639 => 0x61,
0x63A => 0x67,
0x641 => 0x66,
0x642 => 0x6B,
0x643 => 0x6B,
0x644 => 0x6C,
0x645 => 0x6D,
0x646 => 0x6E,
0x647 => 0x68,
0x648 => 0x6F,
0x64A => 0x79,
0x664 => 0x34,
0x665 => 0x35,
0x666 => 0x36,
0x67E => 0x70,
0x686 => 0x63,
0x698 => 0x7A,
0x6A9 => 0x6B,
0x6AF => 0x67,
0x6CC => 0x69,
0x6F0 => 0x30,
0x6F1 => 0x31,
0x6F2 => 0x32,
0x6F3 => 0x33,
0x6F4 => 0x34,
0x6F5 => 0x35,
0x6F6 => 0x36,
0x6F7 => 0x37,
0x6F8 => 0x38,
0x6F9 => 0x39,
0x905 => 0x61,
0x906 => 0x61,
0x907 => 0x69,
0x908 => 0x69,
0x909 => 0x75,
0x90A => 0x75,
0x90D => 0x65,
0x90F => 0x65,
0x910 => 0x61,
0x911 => 0x6F,
0x912 => 0x6F,
0x913 => 0x6F,
0x92C => 0x42,
0x932 => 0x4C,
0x1000 => 0x6B,
0x1002 => 0x67,
0x1005 => 0x73,
0x1007 => 0x7A,
0x1009 => 0x75,
0x100A => 0x69,
0x100B => 0x74,
0x100D => 0x64,
0x1010 => 0x74,
0x1012 => 0x64,
0x1014 => 0x6E,
0x1015 => 0x70,
0x1017 => 0x62,
0x1019 => 0x6D,
0x101A => 0x79,
0x101C => 0x6C,
0x101D => 0x77,
0x101F => 0x68,
0x1021 => 0x61,
0x1023 => 0x69,
0x1027 => 0x65,
0x102B => 0x61,
0x102C => 0x61,
0x102D => 0x6F,
0x102E => 0x69,
0x102F => 0x75,
0x1030 => 0x75,
0x1031 => 0x65,
0x1032 => 0x65,
0x103D => 0x77,
0x103E => 0x68,
0x10D0 => 0x61,
0x10D1 => 0x62,
0x10D2 => 0x67,
0x10D3 => 0x64,
0x10D4 => 0x65,
0x10D5 => 0x76,
0x10D6 => 0x7A,
0x10D7 => 0x74,
0x10D8 => 0x69,
0x10D9 => 0x6B,
0x10DA => 0x6C,
0x10DB => 0x6D,
0x10DC => 0x6E,
0x10DD => 0x6F,
0x10DE => 0x70,
0x10DF => 0x7A,
0x10E0 => 0x72,
0x10E1 => 0x73,
0x10E2 => 0x74,
0x10E3 => 0x75,
0x10E4 => 0x66,
0x10E5 => 0x6B,
0x10E6 => 0x67,
0x10E7 => 0x71,
0x10E8 => 0x73,
0x10E9 => 0x63,
0x10EA => 0x74,
0x10EB => 0x64,
0x10EC => 0x74,
0x10ED => 0x63,
0x10EE => 0x6B,
0x10EF => 0x6A,
0x10F0 => 0x68,
0x1D05 => 0x44,
0x1D06 => 0x44,
0x1D6D => 0x64,
0x1D81 => 0x64,
0x1D91 => 0x64,
0x1E9E => 0x53,
0x1EA0 => 0x41,
0x1EA1 => 0x61,
0x1EA2 => 0x41,
0x1EA3 => 0x61,
0x1EA4 => 0x41,
0x1EA5 => 0x61,
0x1EA6 => 0x41,
0x1EA7 => 0x61,
0x1EA8 => 0x41,
0x1EA9 => 0x61,
0x1EAA => 0x41,
0x1EAB => 0x61,
0x1EAC => 0x41,
0x1EAD => 0x61,
0x1EAE => 0x41,
0x1EAF => 0x61,
0x1EB0 => 0x41,
0x1EB1 => 0x61,
0x1EB2 => 0x41,
0x1EB3 => 0x61,
0x1EB4 => 0x41,
0x1EB5 => 0x61,
0x1EB6 => 0x41,
0x1EB7 => 0x61,
0x1EB8 => 0x45,
0x1EB9 => 0x65,
0x1EBA => 0x45,
0x1EBB => 0x65,
0x1EBC => 0x45,
0x1EBD => 0x65,
0x1EBE => 0x45,
0x1EBF => 0x65,
0x1EC0 => 0x45,
0x1EC1 => 0x65,
0x1EC2 => 0x45,
0x1EC3 => 0x65,
0x1EC4 => 0x45,
0x1EC5 => 0x65,
0x1EC6 => 0x45,
0x1EC7 => 0x65,
0x1EC8 => 0x49,
0x1EC9 => 0x69,
0x1ECA => 0x49,
0x1ECB => 0x69,
0x1ECC => 0x4F,
0x1ECD => 0x6F,
0x1ECE => 0x4F,
0x1ECF => 0x6F,
0x1ED0 => 0x4F,
0x1ED1 => 0x6F,
0x1ED2 => 0x4F,
0x1ED3 => 0x6F,
0x1ED4 => 0x4F,
0x1ED5 => 0x6F,
0x1ED6 => 0x4F,
0x1ED7 => 0x6F,
0x1ED8 => 0x4F,
0x1ED9 => 0x6F,
0x1EDA => 0x4F,
0x1EDB => 0x6F,
0x1EDC => 0x4F,
0x1EDD => 0x6F,
0x1EDE => 0x4F,
0x1EDF => 0x6F,
0x1EE0 => 0x4F,
0x1EE1 => 0x6F,
0x1EE2 => 0x4F,
0x1EE3 => 0x6F,
0x1EE4 => 0x55,
0x1EE5 => 0x75,
0x1EE6 => 0x55,
0x1EE7 => 0x75,
0x1EE8 => 0x55,
0x1EE9 => 0x75,
0x1EEA => 0x55,
0x1EEB => 0x75,
0x1EEC => 0x55,
0x1EED => 0x75,
0x1EEE => 0x55,
0x1EEF => 0x75,
0x1EF0 => 0x55,
0x1EF1 => 0x75,
0x1EF2 => 0x59,
0x1EF3 => 0x79,
0x1EF4 => 0x59,
0x1EF5 => 0x79,
0x1EF6 => 0x59,
0x1EF7 => 0x79,
0x1EF8 => 0x59,
0x1EF9 => 0x79,
0x1F00 => 0x61,
0x1F01 => 0x61,
0x1F02 => 0x61,
0x1F03 => 0x61,
0x1F04 => 0x61,
0x1F05 => 0x61,
0x1F06 => 0x61,
0x1F07 => 0x61,
0x1F08 => 0x41,
0x1F09 => 0x41,
0x1F0A => 0x41,
0x1F0B => 0x41,
0x1F0C => 0x41,
0x1F0D => 0x41,
0x1F0E => 0x41,
0x1F0F => 0x41,
0x1F10 => 0x65,
0x1F11 => 0x65,
0x1F12 => 0x65,
0x1F13 => 0x65,
0x1F14 => 0x65,
0x1F15 => 0x65,
0x1F18 => 0x45,
0x1F19 => 0x45,
0x1F1A => 0x45,
0x1F1B => 0x45,
0x1F1C => 0x45,
0x1F1D => 0x45,
0x1F30 => 0x69,
0x1F31 => 0x69,
0x1F32 => 0x69,
0x1F33 => 0x69,
0x1F34 => 0x69,
0x1F35 => 0x69,
0x1F36 => 0x69,
0x1F37 => 0x69,
0x1F38 => 0x49,
0x1F39 => 0x49,
0x1F3B => 0x49,
0x1F3C => 0x49,
0x1F3D => 0x49,
0x1F3E => 0x49,
0x1F3F => 0x49,
0x1F40 => 0x6F,
0x1F41 => 0x6F,
0x1F42 => 0x6F,
0x1F43 => 0x6F,
0x1F44 => 0x6F,
0x1F45 => 0x6F,
0x1F48 => 0x4F,
0x1F49 => 0x4F,
0x1F4A => 0x4F,
0x1F4B => 0x4F,
0x1F4C => 0x4F,
0x1F4D => 0x4F,
0x1F70 => 0x61,
0x1F72 => 0x65,
0x1F76 => 0x69,
0x1F78 => 0x6F,
0x1F80 => 0x61,
0x1F81 => 0x61,
0x1F82 => 0x61,
0x1F83 => 0x61,
0x1F84 => 0x61,
0x1F85 => 0x61,
0x1F86 => 0x61,
0x1F87 => 0x61,
0x1F88 => 0x41,
0x1F89 => 0x41,
0x1F8A => 0x41,
0x1F8B => 0x41,
0x1F8C => 0x41,
0x1F8D => 0x41,
0x1F8E => 0x41,
0x1F8F => 0x41,
0x1FB0 => 0x61,
0x1FB1 => 0x61,
0x1FB2 => 0x61,
0x1FB3 => 0x61,
0x1FB4 => 0x61,
0x1FB6 => 0x61,
0x1FB7 => 0x61,
0x1FB8 => 0x41,
0x1FB9 => 0x41,
0x1FBA => 0x41,
0x1FBC => 0x41,
0x1FC8 => 0x45,
0x1FD0 => 0x69,
0x1FD1 => 0x69,
0x1FD2 => 0x69,
0x1FD6 => 0x69,
0x1FD7 => 0x69,
0x1FD8 => 0x49,
0x1FD9 => 0x49,
0x1FDA => 0x49,
0x1FE8 => 0x59,
0x1FE9 => 0x59,
0x1FEA => 0x59,
0x1FF8 => 0x4F,
0x2000 => 0x20,
0x2001 => 0x20,
0x2002 => 0x20,
0x2003 => 0x20,
0x2004 => 0x20,
0x2005 => 0x20,
0x2006 => 0x20,
0x2007 => 0x20,
0x2008 => 0x20,
0x2009 => 0x20,
0x200A => 0x20,
0x202F => 0x20,
0x205F => 0x20,
0x2074 => 0x34,
0x2075 => 0x35,
0x2076 => 0x36,
0x2077 => 0x37,
0x2078 => 0x38,
0x2079 => 0x39,
0x2080 => 0x30,
0x2081 => 0x31,
0x2082 => 0x32,
0x2083 => 0x33,
0x2084 => 0x34,
0x2085 => 0x35,
0x2086 => 0x36,
0x2087 => 0x37,
0x2088 => 0x38,
0x2089 => 0x39,
0x3000 => 0x20,
];

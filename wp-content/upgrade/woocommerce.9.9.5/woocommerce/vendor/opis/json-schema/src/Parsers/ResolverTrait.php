<?php
/* ============================================================================
 * Copyright 2020 Zindex Software
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ============================================================================ */

namespace Opis\JsonSchema\Parsers;

use Opis\JsonSchema\Helper;

trait ResolverTrait
{
    /**
     * @param array $list
     * @return array
     */
    protected function resolveSubTypes(array $list): array
    {
        foreach (Helper::JSON_SUBTYPES as $sub => $super) {
            if (!isset($list[$sub]) && isset($list[$super])) {
                $list[$sub] = $list[$super];
            }
        }

        return $list;
    }
}
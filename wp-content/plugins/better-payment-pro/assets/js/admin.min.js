!function(e){"use strict";e(document).on("click",".bp-refund-buttons-wrap a",function(t){t.preventDefault();let n=e(this),o=n.attr("data-orderid"),i=n.attr("data-amount"),a=n.attr("data-refundtype");Swal.fire({title:"Are you sure?",text:"You won't be able to revert this!",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Yes, refund it!",focusCancel:1}).then(t=>{t.value?e.post(betterPaymentPro.ajaxurl,{action:"better_payment_pro_transaction_refund",orderId:o,refundAmount:i,refundType:a,_wpnonce:betterPaymentPro.nonce}).done(function(e){toastr.success("Successfully refunded!"),setTimeout(function(){location.reload()},1500)}).fail(function(){toastr.error("Something went wrong!")}):"cancel"==t.dismiss||t.dismiss})}),e(document).on("click",".bp-send-receipt-submit",function(t){t.preventDefault();let n=e(this),o=e(".better-payment .bp-send-receipt .email-recipient-field").val(),i=n.attr("data-orderid"),a=function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}(o);if(""==o||!a)return toastr.error("Email address is not valid!"),!1;e.post(betterPaymentPro.ajaxurl,{action:"better_payment_pro_transaction_send_receipt",email:o,orderId:i,_wpnonce:betterPaymentPro.nonce}).done(function(e){toastr.success("Email sent successfully!"),setTimeout(function(){location.reload()},1500)}).fail(function(){toastr.error("Something went wrong!")})}),e(document).on("click",".better-payment-print-receipt-btn",function(t){t.preventDefault(),e(this);let n=e(".better-payment-print-receipt-wrap").html(),o=document.createElement("div");o.innerHTML=n;let i=e(window).height(),a=window.open("","","left=50%,top=10%,width=700,height="+i+",toolbar=0,scrollbars=0,status=0");a.document.write(o.outerHTML),a.document.close(),a.focus(),a.print(),a.close()}),e(document).on("click",'.better-payment .bp-license-form-block button[type="submit"][name="license_activate"]',function(t){t.preventDefault(),e(".bp-license-error-msg").hide().text(""),e(".bp-verification-msg").hide();let n=e(this);n.text("Activating..."),e.ajax({url:wpdeveloperLicenseManagerConfig.api_url,type:"POST",data:{action:`${wpdeveloperLicenseManagerConfig.action}/license/activate`,_nonce:wpdeveloperLicenseManagerConfig.nonce,license_key:e(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).val()},success:function(t){if(t.success){if(e(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).attr("disabled","disabled"),"required_otp"!==t.data.license)return e(".bp-activate__license__block").hide().siblings(".--deactivation-form").show(),e(".--deactivation-form input").val(t.data.license_key),void e(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).val("").removeAttr("disabled").siblings("button").removeAttr("disabled").text("Activate");n.text("Verification Required").attr("disabled","disabled").addClass("--verification-required"),e(".bp-customer-email").text(t.data.customer_email),e(".bp-verification-msg").show()}else e(".bp-license-error-msg").text(t.data.message).show(),n.text("Activate")},error:function(e){console.log(e),n.text("Activate")}})}).on("click",'.bp-verification-msg button[type="submit"]',function(t){t.preventDefault(),e(".bp-license-error-msg").hide().text("");let n=e(this);n.text("Verifying..."),e.ajax({url:wpdeveloperLicenseManagerConfig.api_url,type:"POST",data:{action:`${wpdeveloperLicenseManagerConfig.action}/license/submit-otp`,_nonce:wpdeveloperLicenseManagerConfig.nonce,license:e(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).val(),otp:e(`#${wpdeveloperLicenseManagerConfig.action}-license-otp`).val()},success:function(t){t.success?window.location.reload():e(".bp-license-error-msg").text("Whoops! Your License Verification Code has expired. Please try again.").show(),n.text("Verify")},error:function(e){console.log(e),n.text("Verify")}})}).on("click",'.bp-license-form-block button[type="submit"][name="license_deactivate"]',function(t){t.preventDefault();let n=e(this);n.text("Deactivating..."),e.ajax({url:wpdeveloperLicenseManagerConfig.api_url,type:"POST",data:{action:`${wpdeveloperLicenseManagerConfig.action}/license/deactivate`,_nonce:wpdeveloperLicenseManagerConfig.nonce},success:function(t){t.success?window.location.reload():e(".bp-license-error-msg").text(t.data.message).show(),n.text("Deactivate")},error:function(e){console.log(e),n.text("Deactivate")}})}).on("click",".bp-otp-resend",function(t){t.preventDefault(),e.ajax({url:wpdeveloperLicenseManagerConfig.api_url,type:"POST",data:{action:`${wpdeveloperLicenseManagerConfig.action}/license/resend-otp`,_nonce:wpdeveloperLicenseManagerConfig.nonce,license:e(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).val()},success:function(t){t.success?(e(".bp-license-error-msg").text("License Verification Code has been sent to your email address. Please check your email to find the code.").addClass("notice-message").show(),setTimeout(function(){e(".bp-license-error-msg").removeClass("notice-message").text("").hide()},3e3)):e(".bp-license-error-msg").text(t.data.message).show()},error:function(e){console.log(e)}})}),toastr.options={timeOut:"2000",toastClass:"font-size-md",positionClass:"toast-top-center",showMethod:"slideDown",hideMethod:"slideUp"}}(jQuery);
!function(t){"use strict";t(document).ready(function(){function a(t){let a=[],n=[],e={label:"Total Transaction",data:t.all_transactions,borderColor:"#735EF8",backgroundColor:"#735EF8"},s={label:"Completed Transaction",data:t.completed_transactions,borderColor:"#0ECA86",backgroundColor:"#0ECA86"},o={label:"Incomplete Transaction",data:t.incomplete_transactions,borderColor:"#FFDA15",backgroundColor:"#FFDA15"},i={label:"Refunded Transaction",data:t.refund_transactions,borderColor:"#FF0202",backgroundColor:"#FF0202"};return void 0!==t.all_transactions_show&&1==t.all_transactions_show&&a.push(e),void 0!==t.completed_transactions_show&&1==t.completed_transactions_show&&a.push(s),void 0!==t.incomplete_transactions_show&&1==t.incomplete_transactions_show&&a.push(o),void 0!==t.refund_transactions_show&&1==t.refund_transactions_show&&a.push(i),void 0!==t.show_all_four_stats&&1==t.show_all_four_stats&&(a=[e,s,o,i]),void 0!==t.payment_date_period_index&&(n=t.payment_date_period_index),{datasets:a,labels:n}}function n(a,n="analytics_time_period"){var e;"analytics_time_period"===n&&("custom"!==a?t(".analytics_time_period-button-text").html("Last "+((e=a).charAt(0).toUpperCase()+e.slice(1))):t(".analytics_time_period-button-text").html("Custom"))}t(document).on("change",".better-payment .analytics_time_period-custom-range",function(a){!0===t(this).is(":checked")&&t(".better-payment .modal.analytics-custom-time-period").addClass("is-active")}),setTimeout(function(){let t={};void 0!==betterPaymentProAnalytics.analytics&&(t=a(betterPaymentProAnalytics.analytics));const n={type:"line",data:{labels:void 0!==t.labels?t.labels:[],datasets:void 0!==t.datasets?t.datasets:[]},options:{maintainAspectRatio:!1,scaleShowHorizontalLines:!0,scaleShowVerticalLines:!1,bezierCurveTension:.3,responsive:!0,spanGaps:!1,tooltips:{mode:"nearest",position:"nearest",intersect:!1},hover:{position:"nearest",intersect:!1},scales:{y:{ticks:{callback:function(t,a,n){return"$"+t}}}}}};new Chart(document.getElementById("myChart"),n)},1e3),t(document).on("click",".better-payment .bp-analytics-filter-submit",function(n){n.preventDefault(),t(".analytics-chart-wrap").css("opacity",.5),t(this);let e=t("#better-payment-analytics-form");t.post(betterPaymentPro.ajaxurl,{action:"better_payment_pro_analytics_filter",data:e.serialize(),_wpnonce:betterPaymentPro.nonce}).done(function(n){if(void 0!==n.data&&void 0!==n.data.analytics){let e=n.data.analytics,s=a(e);myChart.data={labels:void 0!==s.labels?s.labels:[],datasets:void 0!==s.datasets?s.datasets:[]},myChart.update(),t(".analytics-chart-wrap").css("opacity",1),t(".analytics-total-transaction").attr("title","Filtered Total Transactions: "+e.all_transactions_total),t(".analytics-completed-transaction").attr("title","Filtered Completed Transactions: "+e.completed_transactions_total),t(".analytics-incomplete-transaction").attr("title","Filtered Incomplete Transactions: "+e.incomplete_transactions_total),t(".analytics-refund-transaction").attr("title","Filtered Refund Transactions: "+e.refund_transactions_total)}}).fail(function(){t(".analytics-chart-wrap").css("opacity",1),toastr.error("Something went wrong!")})}),t(document).on("click",".analytics-select-custom-button",function(a){a.preventDefault();let n=t(this).attr("data-target");t(n).toggleClass("is-hidden"),".analytics_transaction_types-dropdown"===n?t(".analytics_time_period-dropdown").hasClass("is-hidden")||t(".analytics_time_period-dropdown").addClass("is-hidden"):t(".analytics_transaction_types-dropdown").hasClass("is-hidden")||t(".analytics_transaction_types-dropdown").addClass("is-hidden")}),t(document).on("click","body",function(a){let n=t(".analytics-select-custom-button-wrap");n.is(a.target)||0!==n.has(a.target).length||(t(".analytics_transaction_types-dropdown").hasClass("is-hidden")||t(".analytics_transaction_types-dropdown").addClass("is-hidden"),t(".analytics_time_period-dropdown").hasClass("is-hidden")||t(".analytics_time_period-dropdown").addClass("is-hidden"))}),t(".analytics-select-custom-button-dropdown input[name='analytics_transaction_types[]']").change(function(){this.checked?"all"!==this.value?(t(".analytics_transaction_types-dropdown .analytics_transaction_types-all").prop("checked",!1),t(".analytics_transaction_types-button-text").html("Transactions")):"all"===this.value&&(t(".analytics_transaction_types-dropdown input[name='analytics_transaction_types[]']").prop("checked",!1),t(".analytics_transaction_types-dropdown .analytics_transaction_types-all").prop("checked",!0),t(".analytics_transaction_types-button-text").html("All Transactions")):t(".analytics_transaction_types-dropdown input[name='analytics_transaction_types[]']:checked").length?t(".analytics_transaction_types-button-text").html("Transactions"):(t(".analytics_transaction_types-dropdown .analytics_transaction_types-all").prop("checked",!0),t(".analytics_transaction_types-button-text").html("All Transactions"))}),t(".analytics-select-custom-button-dropdown input[name='analytics_time_period[]']").change(function(){this.checked?(t(".analytics_time_period-dropdown input[name='analytics_time_period[]']").not(this).prop("checked",!1),n(this.value)):t(".analytics_time_period-dropdown input[name='analytics_time_period[]']:checked").length||(t(".analytics_time_period-dropdown .analytics_time_period-week").prop("checked",!0),n("week"))})})}(jQuery);